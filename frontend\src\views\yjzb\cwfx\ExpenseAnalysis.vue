<template>
  <div class="header" ref="header">
      <div class="header-left">
        <h2>办公费用深度分析</h2>
        <div class="sub"></div>
        <div v-if="!hasValidParams" class="param-warning">
          <el-alert 
            title="参数提示" 
            type="warning" 
            :closable="false"
            show-icon>
            <template #default>
              当前缺少必要参数，请通过以下方式传入：
              <br/>• Props: indicatorId, period
              <br/>• URL参数: ?indicatorId=1&period=2024-12
              <br/>• 当前参数: 指标ID={{ currentParams.indicatorId || '未设置' }}, 期间={{ currentParams.period || '未设置' }}
            </template>
          </el-alert>
        </div>
      </div>
      <div class="header-actions">
        <el-popover placement="bottom-end" v-model="tocVisible" trigger="click" :width="260">
          <div class="toc-popover">
            <div class="toc-title">目录</div>
            <ul>
              <li v-for="s in sections" :key="s.id" :class="{active: activeSection===s.id}">
                <a href="#" @click.prevent="scrollToSection(s.id)">{{ s.order }}. {{ s.title }}</a>
              </li>
            </ul>
          </div>
          <template #reference>
            <el-button size="mini" class="toc-btn" icon="el-icon-menu">目录</el-button>
          </template>
        </el-popover>
        <el-button 
          size="mini" 
          type="primary" 
          @click="rerunAllAiAnalysis"
          style="margin-left: 8px;"
          :loading="isRerunningAll"
        >
          重新分析
        </el-button>
        <el-button 
          size="mini" 
          class="export-pdf-btn"
          @click="exportPDF"
          style="margin-left: 8px;"
        >
          导出PDF
        </el-button>
      </div>
    </div>
  <basic-container class="expense-analysis" ref="scrollContainer">
    

    <!-- 章节内容 -->
    <section v-for="s in sections" :key="s.id" class="section" :id="s.id">
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span>{{ s.order }}. {{ s.title }}</span>
            <!-- <el-tag size="small" type="info">章节</el-tag> -->
          </div>
        </template>

        <!-- 文字描述 -->
        <div class="section-desc">
          <el-alert :title="s.desc" type="info" :closable="false" />
        </div>

        <!-- 数据图表 -->
        <div class="charts">
          <div v-if="s.id==='overview'" class="kpi-row">
            <!-- 当月预警信息（可收缩） -->
            <div class="current-month-warnings">
              <div class="details-header">
                <h4>当月预警信息</h4>
                <el-button 
                  type="text" 
                  @click="toggleCurrentMonthWarnings"
                  :icon="currentMonthWarningsVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                >
                  {{ currentMonthWarningsVisible ? '收起' : '展开' }}
                </el-button>
              </div>
              <el-collapse-transition>
                <div v-show="currentMonthWarningsVisible" class="details-content">
                  <div class="detail-section">
                    <div
                      v-for="warn in currentMonthWarnings"
                      :key="warn.id"
                      class="warning-item-row"
                      @click="onWarningClick(warn)"
                    >
                      <el-alert
                        :title="warn.title"
                        :description="warn.description"
                        :type="warn.type"
                        :closable="false"
                        show-icon
                      />
                    </div>
                  </div>
                </div>
              </el-collapse-transition>
            </div>

            <!-- 当前月份核心指标 -->
            <div class="current-month-section">
              <div class="section-title">
                <h3>当前月份核心指标</h3>
                <el-tag type="primary" size="small">{{ currentMonth }}</el-tag>
              </div>
              <el-row :gutter="12">
                <el-col :md="12" :sm="12" :xs="24">
                  <div class="kpi current-month-kpi">
                    <div class="kpi-title">本月费用</div>
                    <div class="kpi-value">¥{{ formatNumber(kpis.currentMonthExpense) }}</div>
                    <div class="kpi-trend" :class="kpis.currentMonthTrend >= 0 ? 'up' : 'down'">
                      <i :class="kpis.currentMonthTrend >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
                      {{ Math.abs(kpis.currentMonthTrend).toFixed(1) }}%
                    </div>
                  </div>
                </el-col>
                <el-col :md="12" :sm="12" :xs="24">
                  <div class="kpi current-month-kpi">
                    <div class="kpi-title">预算执行率</div>
                    <div class="kpi-value" :class="kpis.budgetExecutionRate > 1.1 ? 'negative' : kpis.budgetExecutionRate < 0.9 ? 'negative' : 'positive'">
                      {{ (kpis.budgetExecutionRate * 100).toFixed(1) }}%
                    </div>
                    <div class="kpi-subtitle">预算 ¥{{ formatNumber(kpis.currentMonthBudget) }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 当前月份明细数据 -->
            <div class="current-month-details">
              <div class="details-header">
                <h4>当前月份明细数据</h4>
                <el-button 
                  type="text" 
                  @click="toggleCurrentMonthDetails"
                  :icon="currentMonthDetailsVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                >
                  {{ currentMonthDetailsVisible ? '收起' : '展开' }}
                </el-button>
              </div>
              
              <el-collapse-transition>
                <div v-show="currentMonthDetailsVisible" class="details-content">
                  <!-- 费用类别明细 -->
                  <div class="detail-section">
                    <h5>费用类别明细</h5>
                    <el-table 
                      :data="currentMonthDetails.categories" 
                      size="small" 
                      border 
                      stripe
                      v-loading="currentMonthDetailsLoading"
                      empty-text="暂无明细数据"
                    >
                      <el-table-column prop="name" label="费用类别" min-width="120" />
                      <el-table-column prop="amount" label="金额(元)" min-width="120">
                        <template #default="{ row }">
                          <span>¥{{ formatNumber(row.amount) }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="ratio" label="占比(%)" width="100">
                        <template #default="{ row }">
                          <span>{{ Number(row.ratio).toFixed(2) }}%</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>

                  

                  <!-- 异常项目 -->
                  <div v-if="currentMonthDetails.anomalies && currentMonthDetails.anomalies.length > 0" class="detail-section">
                    <h5>异常项目</h5>
                    <el-alert
                      v-for="anomaly in currentMonthDetails.anomalies"
                      :key="anomaly.id"
                      :title="anomaly.title"
                      :description="anomaly.description"
                      type="warning"
                      :closable="false"
                      show-icon
                      style="margin-bottom: 8px;"
                    />
                  </div>
                </div>
              </el-collapse-transition>
            </div>


          </div>

          <div v-if="s.id==='structure'" class="structure-grid">
            <div class="structure-table">
              <el-table :data="structure.rows" height="320" border size="small" v-loading="structureLoading" empty-text="暂无数据">
                <el-table-column prop="name" label="费用归因" min-width="120" show-overflow-tooltip />
                <el-table-column prop="amount" label="本月实际支出(元)" min-width="140">
                  <template #default="{ row }">
                    <span v-if="row.amount != null">¥{{ formatNumber(row.amount) }}</span>
                    <span v-else class="text-muted">-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="ratio" label="占比(%)" width="100">
                  <template #default="{ row }">
                    <span v-if="row.ratio != null">{{ Number(row.ratio).toFixed(2) }}%</span>
                    <span v-else class="text-muted">-</span>
                  </template>
                </el-table-column>
              </el-table>
              <div v-if="structure.total && structure.total.amount > 0" class="table-footer">
                <span class="total-label">合计：</span>
                <span class="total-amount">¥{{ formatNumber(structure.total.amount) }}</span>
                <span class="total-ratio">(100.00%)</span>
              </div>
            </div>
            <div class="structure-pie" :ref="refs.category">
              <!-- 调试按钮 -->
              <el-button 
                v-if="structure.rows && structure.rows.length > 0" 
                size="small" 
                type="primary" 
                @click="forceInitPie"
                style="position: absolute; top: 10px; right: 10px; z-index: 10;"
              >
                重新初始化饼图
              </el-button>
              <!-- 测试按钮 -->
              <el-button 
                size="small" 
                type="warning" 
                @click="testECharts"
                style="position: absolute; top: 40px; right: 10px; z-index: 10;"
              >
                测试ECharts
              </el-button>
            </div>
          </div>
          <div v-if="s.id==='trend'" class="trend-grid">
            <!-- 左侧：趋势指标 -->
            <div class="trend-indicators">
              <el-card shadow="never" class="indicator-card">
                <template #header>
                  <div class="card-header">
                    <span>趋势指标对比</span>
                    <el-tag v-if="trendLoading" type="info" size="small">加载中...</el-tag>
                  </div>
                </template>
                
                <div v-if="!trendLoading && trend.indicators" class="indicators-content">
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">当前月份费用</div>
                      <div class="indicator-value">¥{{ formatNumber(trend.indicators.currentValue) }}</div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">历史均值</div>
                      <div class="indicator-value">¥{{ formatNumber(trend.indicators.historicalMean) }}</div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">偏离金额</div>
                      <div class="indicator-value" :class="trend.indicators.currentDeviation >= 0 ? 'negative' : 'positive'">
                        ¥{{ formatNumber(trend.indicators.currentDeviation) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">偏离率</div>
                      <div class="indicator-value" :class="trend.indicators.currentDeviationRate >= 0 ? 'negative' : 'positive'">
                        {{ (trend.indicators.currentDeviationRate * 100).toFixed(1) }}%
                      </div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">Z分数</div>
                      <div class="indicator-value" :class="Math.abs(trend.indicators.currentZScore) <= 1 ? 'positive' : Math.abs(trend.indicators.currentZScore) <= 2 ? 'warning' : 'negative'">
                        {{ trend.indicators.currentZScore.toFixed(2) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">趋势状态</div>
                      <div class="indicator-value">
                        <el-tag size="small" :type="trend.indicators.trendStatus === 'normal' ? 'success' : trend.indicators.trendStatus === 'moderate' ? 'warning' : 'danger'">
                          {{ trend.indicators.trendStatus === 'normal' ? '正常' : trend.indicators.trendStatus === 'moderate' ? '较大' : '异常' }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">趋势方向</div>
                      <div class="indicator-value" :class="trend.indicators.trendDirection === '上升' ? 'negative' : trend.indicators.trendDirection === '下降' ? 'positive' : 'normal'">
                        {{ trend.indicators.trendDirection }}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else-if="!trendLoading" class="empty-content">
                  <el-empty description="暂无趋势数据" />
                </div>
              </el-card>
            </div>
            
            <!-- 右侧：趋势图表 -->
            <div class="trend-chart">
              <div :ref="refs.trend" class="chart"></div>
            </div>
          </div>
          <div v-if="s.id==='volatility'" class="volatility-grid">
            <!-- 左侧：波动性指标 -->
            <div class="volatility-indicators">
              <el-card shadow="never" class="indicator-card">
                <template #header>
                  <div class="card-header">
                    <span>波动性指标</span>
                  </div>
                </template>
                <div v-if="!volatilityLoading && volatility.indicators" class="indicators-content">
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">当前月份费用</div>
                      <div class="indicator-value">¥{{ formatNumber(volatility.indicators.currentValue) }}</div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">历史均值</div>
                      <div class="indicator-value">¥{{ formatNumber(volatility.indicators.historicalMean) }}</div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">偏离金额</div>
                      <div class="indicator-value" :class="volatility.indicators.currentDeviation >= 0 ? 'negative' : 'positive'">
                        ¥{{ formatNumber(volatility.indicators.currentDeviation) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">偏离率</div>
                      <div class="indicator-value" :class="volatility.indicators.currentDeviationRate >= 0 ? 'negative' : 'positive'">
                        {{ (volatility.indicators.currentDeviationRate * 100).toFixed(1) }}%
                      </div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">Z分数</div>
                      <div class="indicator-value" :class="Math.abs(volatility.indicators.currentZScore) <= 1 ? 'positive' : Math.abs(volatility.indicators.currentZScore) <= 2 ? 'warning' : 'negative'">
                        {{ volatility.indicators.currentZScore.toFixed(2) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">波动状态</div>
                      <div class="indicator-value">
                        <el-tag size="small" :type="volatility.indicators.volatilityStatus === 'normal' ? 'success' : volatility.indicators.volatilityStatus === 'moderate' ? 'warning' : 'danger'">
                          {{ volatility.indicators.volatilityStatus === 'normal' ? '正常' : volatility.indicators.volatilityStatus === 'moderate' ? '较大' : '异常' }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">波动方向</div>
                      <div class="indicator-value" :class="volatility.indicators.volatilityDirection === '上升' ? 'negative' : volatility.indicators.volatilityDirection === '下降' ? 'positive' : 'normal'">
                        {{ volatility.indicators.volatilityDirection }}
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else-if="!volatilityLoading" class="empty-content">
                  <el-empty description="暂无波动性数据" />
                </div>
                <div v-else class="loading-content">
                  <el-skeleton :rows="4" animated />
                </div>
              </el-card>
            </div>
            
            <!-- 右侧：控制图 -->
            <div class="volatility-chart">
              <div :ref="refs.anomaly" class="chart"></div>
              
            </div>
          </div>
          <div v-if="s.id==='growth'" class="growth-grid">
            <!-- 左侧：增长率指标 -->
            <div class="growth-indicators">
              <el-card shadow="never" class="indicator-card">
                <template #header>
                  <div class="card-header">
                    <span>增长率指标</span>
                  </div>
                </template>
                <div v-if="!growthLoading && growth.indicators" class="indicators-content">
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">当前月份费用</div>
                      <div class="indicator-value">¥{{ formatNumber(growth.indicators.currentValue) }}</div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">当前环比增长率</div>
                      <div class="indicator-value" :class="growth.indicators.currentMomGrowth >= 0 ? 'positive' : 'negative'">
                        {{ (growth.indicators.currentMomGrowth * 100).toFixed(1) }}%
                      </div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">历史平均增长率</div>
                      <div class="indicator-value" :class="growth.indicators.historicalMomMean >= 0 ? 'positive' : 'negative'">
                        {{ (growth.indicators.historicalMomMean * 100).toFixed(1) }}%
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">增长率偏离</div>
                      <div class="indicator-value" :class="growth.indicators.currentMomDeviation >= 0 ? 'negative' : 'positive'">
                        {{ (growth.indicators.currentMomDeviation * 100).toFixed(1) }}%
                      </div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">Z分数</div>
                      <div class="indicator-value" :class="Math.abs(growth.indicators.currentMomZScore) <= 1 ? 'positive' : Math.abs(growth.indicators.currentMomZScore) <= 2 ? 'warning' : 'negative'">
                        {{ growth.indicators.currentMomZScore.toFixed(2) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">增长率状态</div>
                      <div class="indicator-value">
                        <el-tag size="small" :type="growth.indicators.growthStatus === 'normal' ? 'success' : growth.indicators.growthStatus === 'moderate' ? 'warning' : 'danger'">
                          {{ growth.indicators.growthStatus === 'normal' ? '正常' : growth.indicators.growthStatus === 'moderate' ? '较大' : '异常' }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">增长率方向</div>
                      <div class="indicator-value" :class="growth.indicators.growthDirection === '正增长' ? 'positive' : growth.indicators.growthDirection === '负增长' ? 'negative' : 'normal'">
                        {{ growth.indicators.growthDirection }}
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else-if="!growthLoading" class="empty-content">
                  <el-empty description="暂无增长率数据" />
                </div>
                <div v-else class="loading-content">
                  <el-skeleton :rows="4" animated />
                </div>
              </el-card>
            </div>
            
            <!-- 右侧：增长率图表 -->
            <div class="growth-chart">
              <div :ref="refs.corr" class="chart"></div>
              
            </div>
          </div>
          <div v-if="s.id==='budget'" class="budget-grid">
            <!-- 左侧：预算分析指标 -->
            <div class="budget-indicators">
              <el-card shadow="never" class="indicator-card">
                <template #header>
                  <div class="card-header">
                    <span>预算分析指标</span>
                  </div>
                </template>
                <div v-if="!budgetLoading && budget.indicators" class="indicators-content">
                  <!-- 当月预算执行情况（重点突出） -->
                  <div class="indicator-row" style="padding: 8px; border-radius: 4px; margin-bottom: 12px;">
                    <div class="indicator-item">
                      <div class="indicator-label" style="font-weight: bold; color: #1890ff;">当月预算执行率</div>
                      <div class="indicator-value" :class="budget.indicators.currentMonthExecutionRate > 1.1 ? 'negative' : budget.indicators.currentMonthExecutionRate < 0.9 ? 'negative' : 'positive'" style="font-size: 16px; font-weight: bold;">
                        {{ (budget.indicators.currentMonthExecutionRate * 100).toFixed(1) }}%
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label" style="font-weight: bold; color: #1890ff;">当月预算偏差</div>
                      <div class="indicator-value" :class="budget.indicators.currentMonthVariance > 0 ? 'negative' : 'positive'" style="font-size: 16px; font-weight: bold;">
                        ¥{{ formatNumber(budget.indicators.currentMonthVariance) }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- 全年预算信息 -->
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">全年预算</div>
                      <div class="indicator-value">
                        ¥{{ formatNumber(budget.indicators.annualBudget) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">月度预算</div>
                      <div class="indicator-value">
                        ¥{{ formatNumber(budget.indicators.monthlyBudget) }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- 当月实际vs预算 -->
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">当月实际支出</div>
                      <div class="indicator-value">
                        ¥{{ formatNumber(budget.indicators.currentMonthActual) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">当月预算</div>
                      <div class="indicator-value">
                        ¥{{ formatNumber(budget.indicators.currentMonthBudget) }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- 整体预算执行情况 -->
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">整体预算执行率</div>
                      <div class="indicator-value" :class="budget.indicators.budgetExecutionRate > 1.1 ? 'negative' : budget.indicators.budgetExecutionRate < 0.9 ? 'negative' : 'positive'">
                        {{ (budget.indicators.budgetExecutionRate * 100).toFixed(1) }}%
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">整体预算偏差</div>
                      <div class="indicator-value" :class="budget.indicators.totalVariance > 0 ? 'negative' : 'positive'">
                        ¥{{ formatNumber(budget.indicators.totalVariance) }}
                      </div>
                    </div>
                  </div>
                  

                  
                  <!-- 风险等级 -->
                  <div class="indicator-row">
                    <div class="indicator-item full-width">
                      <div class="indicator-label">风险等级</div>
                      <div class="indicator-value">
                        <el-tag size="small" :type="budget.budgetRisk.riskLevel === 'high' ? 'danger' : budget.budgetRisk.riskLevel === 'medium' ? 'warning' : 'success'">
                          {{ budget.budgetRisk.riskLevel === 'high' ? '高风险' : budget.budgetRisk.riskLevel === 'medium' ? '中等风险' : '低风险' }}
                        </el-tag>
                        <span style="margin-left: 10px; font-size: 12px; color: #666;">
                          ({{ budget.budgetRisk.riskScore }}分)
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else-if="!budgetLoading" class="empty-content">
                  <el-empty description="暂无预算分析数据" />
                </div>
                <div v-else class="loading-content">
                  <el-skeleton :rows="4" animated />
                </div>
              </el-card>
            </div>
            
            <!-- 右侧：预算分析图表 -->
            <div class="budget-chart">
              <div :ref="refs.budget" class="chart"></div>
              
            </div>
          </div>
          <div v-if="s.id==='forecast'" class="forecast-grid">
            <!-- 左侧：预测分析指标 -->
            <div class="forecast-indicators">
              <el-card shadow="never" class="indicator-card">
                <template #header>
                  <div class="card-header">
                    <span>预测分析指标</span>
                    
                  </div>
                </template>
                <div v-if="!forecastLoading && forecast.indicators" class="indicators-content">
                  <!-- 当月预测vs预算对比 -->
                  <div class="indicator-row" style=" padding: 8px; border-radius: 4px; margin-bottom: 12px;">
                    <div class="indicator-item">
                      <div class="indicator-label" style="font-weight: bold; color: #1890ff;">当月实际</div>
                      <div class="indicator-value" style="font-size: 16px; font-weight: bold;">
                        ¥{{ formatNumber(forecast.indicators.currentMonthActual) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label" style="font-weight: bold; color: #1890ff;">当月预算</div>
                      <div class="indicator-value" style="font-size: 16px; font-weight: bold;">
                        ¥{{ formatNumber(forecast.indicators.currentMonthBudget) }}
                      </div>
                    </div>
                  </div>
                  
                  
                  <!-- 趋势方向 -->
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">预测趋势</div>
                      <div class="indicator-value" :class="forecast.indicators.trendDirection === '上升' ? 'negative' : forecast.indicators.trendDirection === '下降' ? 'positive' : 'normal'">
                        {{ forecast.indicators.trendDirection }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">预算执行率</div>
                      <div class="indicator-value" :class="forecast.indicators.currentMonthExecutionRate > 1.1 ? 'negative' : forecast.indicators.currentMonthExecutionRate < 0.9 ? 'negative' : 'positive'">
                        {{ (forecast.indicators.currentMonthExecutionRate * 100).toFixed(1) }}%
                      </div>
                    </div>
                  </div>
                  
                  <!-- 整体对比 -->
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">累计实际</div>
                      <div class="indicator-value">
                        ¥{{ formatNumber(forecast.indicators.totalActual) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">累计预算</div>
                      <div class="indicator-value">
                        ¥{{ formatNumber(forecast.indicators.totalBudget) }}
                      </div>
                    </div>
                  </div>
                  
                  <!-- 偏差分析 -->
                  <div class="indicator-row">
                    <div class="indicator-item">
                      <div class="indicator-label">当月偏差</div>
                      <div class="indicator-value" :class="forecast.indicators.currentMonthVariance > 0 ? 'negative' : 'positive'">
                        ¥{{ formatNumber(forecast.indicators.currentMonthVariance) }}
                      </div>
                    </div>
                    <div class="indicator-item">
                      <div class="indicator-label">整体偏差</div>
                      <div class="indicator-value" :class="forecast.indicators.totalVariance > 0 ? 'negative' : 'positive'">
                        ¥{{ formatNumber(forecast.indicators.totalVariance) }}
                      </div>
                    </div>
                  </div>
                  
                  
                </div>
                <div v-else-if="!forecastLoading" class="empty-content">
                  <el-empty description="暂无预测分析数据" />
                </div>
                <div v-else class="loading-content">
                  <el-skeleton :rows="4" animated />
                </div>
              </el-card>
            </div>
            
            <!-- 右侧：预测分析图表（仅折线图） -->
            <div class="forecast-chart">
              <div id="forecastTrendChart" class="chart"></div>
            </div>
          </div>
        </div>

        <!-- 分析结论 -->
        <div class="conclusion">
          <el-divider>分析结论</el-divider>
          <div v-if="aiAnalysis[s.id].answerText" class="ai-conclusion-content" v-html="getAiAnswerHtml(s.id)"></div>
          <div v-else class="no-ai-content">
            <p style="color: #999; font-style: italic;">暂无AI解读内容，请点击上方"重新分析"按钮生成AI解读。</p>
          </div>
        </div>
      </el-card>
    </section>
  </basic-container>
</template>

<script>
import * as echarts from 'echarts';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import { getCategoryStructure, getTrendComparison, getVolatilityAnalysis, getGrowthAnalysis, getBudgetAnalysis, getForecastAnalysis } from '@/api/yjzb/indicatorDeepAnalysis';
import { executeExpenseAnalysis, getExpenseAnalysisResult, hasCachedExpenseAnalysis, executeExpenseForecast, getExpenseForecastResult } from '@/api/yjzb/expenseAiAnalysis';

export default {
  props: {
    indicatorId: { type: [String, Number], default: null },
    period: { type: String, default: '' }
  },
  name: 'ExpenseAnalysis',
  data() {
    return {
      refs: {
        trend: 'trendChart',
        category: 'categoryChart',
        dept: 'deptChart',
        anomaly: 'anomalyChart',
        budget: 'budgetChart',
        corr: 'corrChart',
        forecast: 'forecastChart',
      },
      tocVisible: false,
      charts: {},
      // 用于在真实数据到达后刷新KPI
      overviewKpiReady: { trend: false, budget: false },
      structure: { title: '', columns: [], rows: [], total: { amount: 0, ratio: 100 } },
      structureLoading: false,
      trend: { 
        monthlyData: [],
        indicators: {
          currentValue: 0,
          historicalMean: 0,
          historicalStdDev: 0,
          currentDeviation: 0,
          currentDeviationRate: 0,
          currentZScore: 0,
          trendStatus: 'normal',
          trendLevel: 'low',
          trendDirection: '平稳',
          historicalRank: 0,
          percentile: 0,
          historicalCV: 0,
          historicalRange: 0
        },
        trendLimits: {
          upper3Sigma: 0,
          upper2Sigma: 0,
          lower2Sigma: 0,
          lower3Sigma: 0,
          historicalMean: 0
        },
        trendAnalysis: {
          assessment: '',
          directionDescription: '',
          amplitudeDescription: '',
          trendPersistence: '',
          currentMonth: '',
          historicalPeriod: ''
        }
      },
      trendLoading: false,
      volatility: {
        monthlyData: [],
        indicators: {
          currentValue: 0,
          historicalMean: 0,
          historicalStdDev: 0,
          currentDeviation: 0,
          currentDeviationRate: 0,
          currentZScore: 0,
          volatilityStatus: 'normal',
          volatilityLevel: 'low',
          volatilityDirection: '持平',
          historicalRank: 0,
          percentile: 0,
          historicalCV: 0,
          historicalRange: 0
        },
        controlLimits: {
          upper3Sigma: 0,
          upper2Sigma: 0,
          lower2Sigma: 0,
          lower3Sigma: 0,
          historicalMean: 0
        },
        volatilityAnalysis: {
          assessment: '',
          directionDescription: '',
          amplitudeDescription: '',
          seasonalAnalysis: '',
          currentMonth: '',
          historicalPeriod: ''
        }
      },
      volatilityLoading: false,
      growth: {
        monthlyData: [],
        indicators: {
          currentValue: 0,
          currentMomGrowth: 0,
          currentYoyGrowth: 0,
          historicalMomMean: 0,
          historicalMomStdDev: 0,
          currentMomDeviation: 0,
          currentMomZScore: 0,
          growthStatus: 'normal',
          growthLevel: 'low',
          growthDirection: '零增长',
          historicalRank: 0,
          percentile: 0,
          historicalMomCV: 0,
          historicalMomRange: 0
        },
        growthLimits: {
          upper3Sigma: 0,
          upper2Sigma: 0,
          lower2Sigma: 0,
          lower3Sigma: 0,
          historicalMean: 0
        },
        growthAnalysis: {
          assessment: '',
          directionDescription: '',
          amplitudeDescription: '',
          growthTrend: '',
          currentMonth: '',
          historicalPeriod: ''
        }
      },
      growthLoading: false,
      budget: {
        monthlyData: [],
        indicators: {
          totalBudget: 0,
          totalActual: 0,
          totalVariance: 0,
          budgetExecutionRate: 0,
          overBudgetMonths: 0,
          underBudgetMonths: 0,
          maxOverBudget: 0,
          maxUnderBudget: 0
        },
        budgetRisk: {
          riskLevel: 'low',
          riskScore: 0,
          riskFactors: []
        }
      },
      budgetLoading: false,
      forecast: {
        monthlyData: [],
        indicators: {
          currentMonthActual: 0,
          currentMonthBudget: 0,
          currentMonthVariance: 0,
          currentMonthExecutionRate: 0,
          totalActual: 0,
          totalBudget: 0,
          totalVariance: 0,
          overallExecutionRate: 0,
          forecastAccuracy: 0,
          trendDirection: '',
          confidenceLevel: 0
        },
        forecastData: {
          historicalData: [],
          predictedData: [],
          budgetData: [],
          predictionMethod: '',
          confidenceInterval: {}
        }
      },
      forecastLoading: false,
      // 预测分析AI相关
      forecastAiLoading: false,
      forecastAiContent: null,
      forecastAiThinkCollapsed: true,
      forecastAiThinkText: '',
      forecastAiAnswerText: '',
      forecastTrendChartInstance: null,
      
      // 各章节AI分析相关
      aiAnalysis: {
        overview: { loading: false, content: null, thinkText: '', answerText: '', thinkCollapsed: true },
        structure: { loading: false, content: null, thinkText: '', answerText: '', thinkCollapsed: true },
        trend: { loading: false, content: null, thinkText: '', answerText: '', thinkCollapsed: true },
        volatility: { loading: false, content: null, thinkText: '', answerText: '', thinkCollapsed: true },
        growth: { loading: false, content: null, thinkText: '', answerText: '', thinkCollapsed: true },
        budget: { loading: false, content: null, thinkText: '', answerText: '', thinkCollapsed: true },
        forecast: { loading: false, content: null, thinkText: '', answerText: '', thinkCollapsed: true }
      },
      isTestingAi: false,
      isRerunningAll: false,
      activeSection: 'overview',
      sections: [
        { id: 'overview',   order: 1, title: '指标概览',   desc: '汇总期间的总体办公费用情况包括销售和管理费用。' },
        { id: 'structure',  order: 2, title: '归因分析',   desc: '按类别维度展示结构与占比，识别重点项。' },
        { id: 'trend',      order: 3, title: '趋势分析',   desc: '展示月度变化趋势、拐点与季节性。' },
        { id: 'volatility', order: 4, title: '波动性分析', desc: '对波动幅度、标准差、控制图等进行评估。' },
        { id: 'growth',     order: 5, title: '增长率分析', desc: '按环比/同比等维度分析增长率与驱动因素。' },
        { id: 'budget',     order: 6, title: '预算分析',   desc: '以当月为中心，结合全年预算进行预算执行分析，评估偏差与风险。偏差值在水上为超预算，在水下为低于预算。' },
        { id: 'forecast',   order: 7, title: '预测分析',   desc: '基于历史数据AI预测未来趋势，并结合预算数据进行比对分析。' }
      ],
      months: [
        '2023-07','2023-08','2023-09','2023-10','2023-11','2023-12',
        '2024-01','2024-02','2024-03','2024-04','2024-05','2024-06'
      ],
      seriesMonthly: [
        120000, 98000, 110000, 150000, 142000, 130000,
        125000, 90000, 118000, 160000, 266487, 145000
      ],
      categories: [
        { name: '办公用品', value: 420000 },
        { name: '印刷费', value: 165000 },
        { name: '邮寄费', value: 80000 },
        { name: '差旅费', value: 230000 },
        { name: '其他杂项', value: 300000 },
      ],
      deptSpend: [
        { dept: '本部', value: 520000 },
        { dept: '阳春分公司', value: 210000 },
        { dept: '阳东分公司', value: 180000 },
        { dept: '海陵试验区', value: 120000 },
      ],
      budgetVsActual: [
        { month: '2024-04', budget: 120000, actual: 160000 },
        { month: '2024-05', budget: 130000, actual: 266487 },
        { month: '2024-06', budget: 130000, actual: 145000 },
      ],
      corrMatrix: [
        [1.00, 0.72, 0.30, -0.10, 0.55],
        [0.72, 1.00, 0.12, -0.25, 0.48],
        [0.30, 0.12, 1.00, -0.05, 0.18],
        [-0.10, -0.25, -0.05, 1.00, -0.20],
        [0.55, 0.48, 0.18, -0.20, 1.00],
      ],
      corrLabels: ['办公用品','印刷费','邮寄费','差旅费','其他杂项'],
      kpis: { 
        totalExpense: 0, 
        avgMonthly: 0, 
        cv: 0, 
        anomalyMonths: 0,
        // 当前月份相关指标
        currentMonthExpense: 0,
        currentMonthTrend: 0,
        budgetExecutionRate: 1.0,
        currentMonthBudget: 0,
        currentMonthZScore: 0,
        currentMonthRank: 0,
        totalMonths: 12,
        currentMonthPercentile: 0
      },
      // 当前月份明细数据
      currentMonthDetails: {
        categories: [],
        departments: [],
        anomalies: []
      },
      // 当月预警信息（模拟）
      currentMonthWarningsVisible: true,
      currentMonthWarnings: [
        { id: 1, title: '办公用品本月支出异常偏高', description: '较历史均值偏高 45%，请关注采购合规与价差。', type: 'warning', target: 'structure' },
        { id: 2, title: '印刷费接近预算上限', description: '本月执行率已达 92%，建议评估下月需求与预算调整。', type: 'info', target: 'trend' },
        { id: 3, title: '差旅费同比上升明显', description: '同比增长 38%，请核对差旅审批与行程复核。', type: 'warning', target: 'volatility' }
      ],
      currentMonthDetailsVisible: false,
      currentMonthDetailsLoading: false,
      currentMonth: '',
              conclusions: {
        overview: [], structure: [], trend: [], volatility: [], growth: [], budget: [], forecast: [], advice: []
      }
    }
  },
  watch: {
    // 监听props变化，重新加载数据
    indicatorId: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal) {
          this.loadStructure();
        }
      },
      immediate: false
    },
    period: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal) {
          this.loadStructure();
        }
      },
      immediate: false
    }
  },
  computed: {
    // 计算当前参数状态
    currentParams() {
      return {
        indicatorId: this.indicatorId || this.$attrs.indicatorId || this.$route.query.indicatorId,
        period: this.period || this.$attrs.period || this.$route.query.period
      };
    },
    hasValidParams() {
      const { indicatorId, period } = this.currentParams;
      return indicatorId && indicatorId !== 0 && period && period.trim() !== '';
    }
  },
  mounted() {
    console.log('ExpenseAnalysis 组件 mounted');
    this.computeKpisAndConclusions();
    // 初始不触发AI分析，等待用户点击“重新分析”
    
    // 使用双重 nextTick 确保DOM完全渲染
            this.$nextTick(() => {
          console.log('第一次 nextTick，开始初始化图表');
          this.$nextTick(() => {
            console.log('第二次 nextTick，DOM应该完全渲染');
            // 绑定滚动，驱动工具栏固定效果（当容器滚动时也生效）
            try {
              const sc = this.$refs.scrollContainer?.$el || this.$refs.scrollContainer;
              if (sc && sc.addEventListener) sc.addEventListener('scroll', this.syncHeaderFixed, { passive: true });
              window.addEventListener('scroll', this.syncHeaderFixed, { passive: true });
              this.$nextTick(this.syncHeaderFixed);
            } catch (e) { console.warn('绑定工具栏固定事件失败', e); }
            this.loadStructure();
            this.loadTrendData();
            this.loadVolatilityData();
            this.loadGrowthData();
            this.loadBudgetData();
            this.loadForecastData();
            // 等待数据加载完成后再初始化图表
            this.$nextTick(() => {
              this.initCharts();
              // 图表与数据准备完成后，尝试仅读取历史AI缓存并显示
              this.loadCachedAiResults();
            });
            window.addEventListener('resize', this.handleResize);
            // 监听容器内滚动
            const sc = this.$refs.scrollContainer?.$el || this.$refs.scrollContainer;
            if (sc && sc.addEventListener) sc.addEventListener('scroll', this.onScroll, { passive: true });
          });
        });
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
    const sc = this.$refs.scrollContainer?.$el || this.$refs.scrollContainer;
    if (sc && sc.removeEventListener) sc.removeEventListener('scroll', this.onScroll);
    try {
      if (sc && sc.removeEventListener) sc.removeEventListener('scroll', this.syncHeaderFixed);
      window.removeEventListener('scroll', this.syncHeaderFixed);
    } catch {}
    Object.values(this.charts).forEach(c => c && c.dispose());
    // 清理预测图表实例
    if (this.forecastTrendChartInstance) {
      this.forecastTrendChartInstance.dispose();
    }
  },
  methods: {
    // 同步头部固定状态（兼容容器滚动和窗口滚动）
    syncHeaderFixed() {
      try {
        const container = this.$refs.scrollContainer?.$el || this.$refs.scrollContainer;
        const header = this.$refs.header;
        if (!container || !header) return;
        const cRect = container.getBoundingClientRect ? container.getBoundingClientRect() : { top: 0, left: 0, width: container.clientWidth };
        const shouldFix = cRect.top <= 0; // 当容器顶部到达窗口顶端时固定
        header.classList.toggle('is-fixed', shouldFix);
        // 固定时让头部宽度与容器一致
        if (shouldFix) {
          header.style.width = (cRect.width || container.clientWidth) + 'px';
        } else {
          header.style.width = '';
        }
      } catch {}
    },
    // 使用后端返回的真实数据更新第一章“当前月份核心指标”
    updateOverviewKpisFromApis() {
      try {
        // 仅当趋势或预算任一数据有了就可更新，优先用趋势的当前值
        const { period } = this.currentParams;
        if (period && typeof period === 'string' && period.trim() !== '') {
          this.currentMonth = period;
        }

        const trendInd = this.trend && this.trend.indicators ? this.trend.indicators : null;
        const budgetInd = this.budget && this.budget.indicators ? this.budget.indicators : null;

        const next = { ...this.kpis };

        // 当前月实际值、Z分数、环比
        if (trendInd) {
          if (typeof trendInd.currentValue === 'number') {
            next.currentMonthExpense = trendInd.currentValue;
          }
          if (typeof trendInd.currentZScore === 'number') {
            next.currentMonthZScore = trendInd.currentZScore;
          }

          // 采用月度数组计算环比
          const md = Array.isArray(this.trend?.monthlyData) ? this.trend.monthlyData : [];
          if (md.length >= 2) {
            const last = Number(md[md.length - 1]?.value);
            const prev = Number(md[md.length - 2]?.value);
            if (isFinite(last) && isFinite(prev) && prev !== 0) {
              next.currentMonthTrend = ((last - prev) / prev) * 100;
            }
          } else if (typeof trendInd.currentDeviationRate === 'number') {
            // 兜底：用相对历史均值的偏差率
            next.currentMonthTrend = trendInd.currentDeviationRate * 100;
          }
        }

        // 预算执行率 = 当年累计实际/全年预算（来自预算接口的 overallExecutionRate 或 totalActual/totalBudget）
        if (budgetInd) {
          const totalActual = Number(budgetInd.totalActual);
          const totalBudget = Number(budgetInd.totalBudget);
          if (isFinite(budgetInd.overallExecutionRate)) {
            next.budgetExecutionRate = budgetInd.overallExecutionRate;
          } else if (isFinite(totalActual) && isFinite(totalBudget) && totalBudget !== 0) {
            next.budgetExecutionRate = totalActual / totalBudget;
          }
          if (isFinite(budgetInd.currentMonthBudget)) {
            next.currentMonthBudget = budgetInd.currentMonthBudget;
          }
        }

        this.kpis = next;
      } catch (e) {
        console.warn('updateOverviewKpisFromApis 失败：', e);
      }
    },
    exportPDF() {
      try {
        window.print();
      } catch (e) {
        console.error('触发打印失败', e);
      }
    },
    formatNumber(v) {
      if (v == null) return '-';
      return (v).toLocaleString();
    },
    // 切换当前月份明细显示
    toggleCurrentMonthDetails() {
      this.currentMonthDetailsVisible = !this.currentMonthDetailsVisible;
      if (this.currentMonthDetailsVisible && this.currentMonthDetails.categories.length === 0) {
        this.loadCurrentMonthDetails();
      }
    },
    // 切换当月预警信息显示
    toggleCurrentMonthWarnings() {
      this.currentMonthWarningsVisible = !this.currentMonthWarningsVisible;
    },
    // 预警点击跳转到对应章节
    onWarningClick(warn) {
      try {
        const id = warn?.target;
        if (typeof id === 'string' && id.trim() !== '') {
          this.scrollToSection(id);
        }
      } catch {}
    },
    // 加载当前月份明细数据
    async loadCurrentMonthDetails() {
      try {
        this.currentMonthDetailsLoading = true;
        
        const { indicatorId, period } = this.currentParams;
        
        if (!this.hasValidParams) {
          console.warn('参数不完整，无法加载当前月份明细数据');
          return;
        }
        // 1) 分类维度：使用后端深度分析接口的“费用结构占比”真实数据
        const resp = await getCategoryStructure(indicatorId, period);
        const rows = resp?.data?.data?.rows || [];
        const categories = Array.isArray(rows)
          ? rows.map(item => ({
              name: item.name,
              amount: item.amount,
              ratio: item.ratio,
              // 暂无分类级别的环比/状态接口，这里先保持为默认值
              trend: 0,
              status: 'normal'
            }))
          : [];

        // 2) 异常：当前无接口，先置为空
        const anomalies = [];
        
        this.currentMonthDetails = { categories, departments: [], anomalies };
        
      } catch (error) {
        console.error('加载当前月份明细数据失败:', error);
        this.$message.error('加载当前月份明细数据失败');
      } finally {
        this.currentMonthDetailsLoading = false;
      }
    },
    // 统一的图表初始化方法
    initCharts() {
      console.log('开始初始化所有图表');
      try {
        this.initTrend();
        this.initDept();
        this.initVolatility();
        this.initVolatility();
        this.initBudget();
        this.initCorr();
        this.initForecast();
        console.log('所有图表初始化完成');
      } catch (error) {
        console.error('图表初始化过程中发生错误:', error);
      }
    },
    // 手动触发饼图初始化（用于调试）
    forceInitPie() {
      console.log('手动触发饼图初始化');
      this.$nextTick(() => {
        this.initStructurePie();
      });
    },
    
    
    // 手动触发预测图表初始化（用于调试）
    forceInitForecast() {
      console.log('手动触发预测图表初始化');
      this.$nextTick(() => {
        this.initForecast();
      });
    },
    // 测试预测数据
    testForecastData() {
      console.log('=== 测试预测数据 ===');
      console.log('预测数据结构:', this.forecast);
      console.log('monthlyData:', this.forecast.monthlyData);
      console.log('indicators:', this.forecast.indicators);
      console.log('forecastData:', this.forecast.forecastData);
      
      if (this.forecast.monthlyData && this.forecast.monthlyData.length > 0) {
        console.log('✅ 预测数据存在');
        this.$message.success('预测数据正常');
      } else {
        console.warn('⚠️ 预测数据为空');
        this.$message.warning('预测数据为空，请检查API调用');
      }
    },

    // 预测分析AI相关方法
    async loadForecastAiAnalysis() {
      try {
        this.forecastAiLoading = true;
        const { indicatorId, period } = this.currentParams;
        
        if (!this.hasValidParams) {
          console.warn('参数不完整，无法加载预测AI分析');
          return;
        }

        // 这里应该调用预测分析的AI接口
        // 暂时使用模拟数据
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟AI分析结果
        this.forecastAiContent = {
          executeStatus: 'COMPLETED',
          result: `<think>
分析预测数据的关键要点：
1. 当前月份实际支出与预算的对比
2. 历史趋势分析
3. 预测准确度评估
4. 未来趋势预测
5. 风险因素识别
</think>

## 预测分析结果

### 📊 当前状况
- **当月实际支出**: ¥${this.formatNumber(this.forecast.indicators?.currentMonthActual || 0)}
- **当月预算**: ¥${this.formatNumber(this.forecast.indicators?.currentMonthBudget || 0)}
- **预算执行率**: ${((this.forecast.indicators?.currentMonthExecutionRate || 1) * 100).toFixed(1)}%

### 📈 趋势分析
基于历史数据分析，当前费用呈现${this.forecast.indicators?.trendDirection || '平稳'}趋势。

### 🎯 预测准确度
- **模型准确度**: ${((this.forecast.indicators?.forecastAccuracy || 0.85) * 100).toFixed(1)}%
- **置信度**: ${((this.forecast.indicators?.confidenceLevel || 0.8) * 100).toFixed(1)}%

### ⚠️ 风险提示
${this.forecast.indicators?.currentMonthExecutionRate > 1.1 ? '当前预算执行率过高，存在超支风险，建议加强成本控制。' : this.forecast.indicators?.currentMonthExecutionRate < 0.9 ? '当前预算执行率偏低，可能存在预算浪费，建议优化资源配置。' : '当前预算执行情况良好，继续保持。'}

### 💡 建议措施
1. 持续监控费用趋势变化
2. 根据预测结果调整预算分配
3. 建立预警机制，及时应对异常情况
4. 定期评估预测模型准确性，优化算法`
        };
        
        this.parseForecastAiResultText(this.forecastAiContent.result || '');
        
      } catch (error) {
        console.error('加载预测AI分析失败:', error);
        this.$message.error('加载预测AI分析失败');
      } finally {
        this.forecastAiLoading = false;
      }
    },

    async rerunForecastAnalysis() {
      try {
        this.forecastAiLoading = true;
        this.$message.success('已重新开始预测AI分析，稍后自动刷新结果');
        
        // 模拟重新分析过程
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await this.loadForecastAiAnalysis();
        
      } catch (error) {
        console.error('重新预测分析失败:', error);
        this.$message.error('重新预测分析失败');
      } finally {
        this.forecastAiLoading = false;
      }
    },

    parseForecastAiResultText(text) {
      try {
        const pattern = /<think>([\s\S]*?)<\/think>/i;
        const match = typeof text === 'string' ? text.match(pattern) : null;
        if (match && match[1] != null) {
          this.forecastAiThinkText = match[1].trim();
          this.forecastAiAnswerText = text.replace(pattern, '').trim();
        } else {
          this.forecastAiThinkText = '';
          this.forecastAiAnswerText = (text || '').trim();
        }
      } catch (e) {
        this.forecastAiThinkText = '';
        this.forecastAiAnswerText = (text || '').trim();
      }
    },

    forecastAiAnswerHtml() {
      const text = this.forecastAiAnswerText || '';
      const raw = marked.parse(text, { breaks: true, gfm: true });
      const safe = DOMPurify.sanitize(raw);
      return safe;
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        'COMPLETED': 'success',
        'PROCESSING': 'warning',
        'PENDING': 'info',
        'FAILED': 'danger',
        'RUNNING': 'warning'
      };
      return statusMap[status] || 'info';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'COMPLETED': '已完成',
        'PROCESSING': '计算中',
        'PENDING': '待计算',
        'FAILED': '失败',
        'RUNNING': '分析中'
      };
      return statusMap[status] || status;
    },
    
    // AI分析相关方法
    async rerunAiAnalysis(sectionId) {
      try {
        this.aiAnalysis[sectionId].loading = true;
        this.$message.success(`已重新开始${this.getSectionTitle(sectionId)}AI分析，稍后自动刷新结果`);
        
        // 根据章节ID调用对应的分析方法
        await this.runSectionAiAnalysis(sectionId, { force: true });
        
      } catch (error) {
        console.error(`重新${this.getSectionTitle(sectionId)}分析失败:`, error);
        this.$message.error(`重新${this.getSectionTitle(sectionId)}分析失败`);
      } finally {
        this.aiAnalysis[sectionId].loading = false;
      }
    },
    
    // 根据章节ID运行对应的AI分析
    async runSectionAiAnalysis(sectionId, options = {}) {
      try {
        console.log(`开始${this.getSectionTitle(sectionId)}AI分析`);
        const { indicatorId, period } = this.currentParams;
        const force = options && options.force === true;
        
        if (!this.hasValidParams) {
          console.warn('参数不完整，无法进行AI分析');
          this.$message.warning('请先设置指标ID和期间参数');
          return;
        }
        
        let response;
        if (!force) {
          // 非强制模式下，先检查缓存
          const hasCache = await hasCachedExpenseAnalysis({
            indicatorId,
            period,
            analysisType: sectionId
          });
          if (hasCache.data) {
            console.log(`发现${this.getSectionTitle(sectionId)}缓存结果，直接获取`);
            response = await getExpenseAnalysisResult({
              indicatorId,
              period,
              analysisType: sectionId
            });
          }
        }

        // 若强制刷新或无缓存，则执行分析
        if (!response) {
          const inputParams = this.prepareInputParams(sectionId);
          response = await executeExpenseAnalysis({
            indicatorId,
            period,
            analysisType: sectionId,
            inputParams: JSON.stringify(inputParams),
            force: true === force
          });
        }
        
        if (response?.data?.data) {
          console.log(`${this.getSectionTitle(sectionId)}AI分析返回数据:`, response.data.data);
          // 解析AI分析结果
          this.parseAiAnalysisResult(sectionId, response.data.data);
        } else if (response?.data?.msg) {
          console.warn('AI分析业务错误:', response.data.msg);
          this.$message.warning(response.data.msg);
        } else {
          console.warn('AI分析接口返回数据格式异常:', response);
          this.$message.warning('AI分析接口返回数据格式异常');
          // 不再使用模拟数据，保持为空以显示“暂无AI解读内容”
          this.aiAnalysis[sectionId].thinkText = '';
          this.aiAnalysis[sectionId].answerText = '';
        }
        
      } catch (error) {
        console.error(`${this.getSectionTitle(sectionId)}AI分析失败:`, error);
        let errorMsg = `${this.getSectionTitle(sectionId)}AI分析失败`;
        if (error.response?.data?.msg) {
          errorMsg += '：' + error.response.data.msg;
        } else if (error.message) {
          errorMsg += '：' + error.message;
        }
        this.$message.error(errorMsg);
        // 不再使用模拟数据，保持为空以显示“暂无AI解读内容”
        this.aiAnalysis[sectionId].thinkText = '';
        this.aiAnalysis[sectionId].answerText = '';
      }
    },
    
    // 准备输入参数
    prepareInputParams(sectionId) {
      const baseData = {
        indicatorId: this.currentParams.indicatorId,
        period: this.currentParams.period,
        currentParams: this.currentParams
      };
      
      switch (sectionId) {
        case 'overview':
          return {
            ...baseData,
            kpis: this.kpis,
            currentMonthDetails: this.currentMonthDetails,
            currentMonth: this.currentMonth
          };
          
        case 'structure':
          return {
            ...baseData,
            structure: this.structure
          };
          
        case 'trend':
          return {
            ...baseData,
            trend: this.trend
          };
          
        case 'volatility':
          return {
            ...baseData,
            volatility: this.volatility
          };
          
        case 'growth':
          return {
            ...baseData,
            growth: this.growth
          };
          
        case 'budget':
          return {
            ...baseData,
            budget: this.budget
          };
          
        case 'forecast':
          return {
            ...baseData,
            forecast: this.forecast
          };
          
        default:
          console.warn('未知的章节ID:', sectionId);
          return baseData;
      }
    },
    
    // 解析AI分析结果
    parseAiAnalysisResult(sectionId, result) {
      try {
        console.log(`${this.getSectionTitle(sectionId)}开始解析AI分析结果，原始数据类型:`, typeof result, '值:', result);
        
        // 确保result是字符串类型
        let resultString = '';
        if (typeof result === 'string') {
          resultString = result;
        } else if (result && typeof result === 'object') {
          // 如果是对象，尝试提取文本内容；若无可用字段则视为无内容
          if (result.answerContent) {
            resultString = result.answerContent;
          } else if (result.result) {
            resultString = result.result;
          } else if (result.content) {
            resultString = result.content;
          } else {
            resultString = '';
          }
        } else if (result != null) {
          resultString = String(result);
        }
        
        console.log(`${this.getSectionTitle(sectionId)}转换后的字符串:`, resultString);
        
        // 假设Dify返回的结果包含思考过程和答案
        const pattern = /<think>([\s\S]*?)<\/think>/i;
        const match = resultString.match(pattern);
        
        if (match && match[1] != null) {
          this.aiAnalysis[sectionId].thinkText = match[1].trim();
          this.aiAnalysis[sectionId].answerText = resultString.replace(pattern, '').trim();
        } else {
          this.aiAnalysis[sectionId].thinkText = '';
          this.aiAnalysis[sectionId].answerText = resultString.trim();
        }
        
        // 设置分析内容（仅在有文本时标记完成）
        if (resultString && resultString.trim().length > 0) {
          this.aiAnalysis[sectionId].content = {
            executeStatus: 'COMPLETED',
            result: resultString
          };
        } else {
          this.aiAnalysis[sectionId].content = null;
        }
        
        console.log(`${this.getSectionTitle(sectionId)}AI分析结果解析完成`);
        
      } catch (error) {
        console.error(`解析${this.getSectionTitle(sectionId)}AI分析结果失败:`, error, '原始数据:', result);
        this.aiAnalysis[sectionId].thinkText = '';
        this.aiAnalysis[sectionId].answerText = '';
      }
    },
    
    // 获取AI答案的HTML
    getAiAnswerHtml(sectionId) {
      const text = this.aiAnalysis[sectionId].answerText || '';
      const raw = marked.parse(text, { breaks: true, gfm: true });
      const safe = DOMPurify.sanitize(raw);
      return safe;
    },
    
    // 获取章节标题
    getSectionTitle(sectionId) {
      const section = this.sections.find(s => s.id === sectionId);
      return section ? section.title : sectionId;
    },
    
    // 初始化所有章节的AI分析
    async initAllAiAnalysis() {
      // 为每个章节启动AI分析
      const sections = ['overview', 'structure', 'trend', 'volatility', 'growth', 'budget', 'forecast'];
      
      for (const sectionId of sections) {
        // 延迟启动，避免同时发起太多请求
        setTimeout(() => {
          this.runSectionAiAnalysis(sectionId);
        }, sections.indexOf(sectionId) * 1000); // 每个章节间隔1秒启动
      }
    },
    
    // 重新分析所有章节（仅强制刷新，触发新的AI调用）
    async rerunAllAiAnalysis() {
      try {
        this.isRerunningAll = true;
        this.$message.success('开始重新分析所有章节，请稍后查看结果');
        const sections = ['overview', 'structure', 'trend', 'volatility', 'growth', 'budget', 'forecast'];
        await Promise.all(sections.map(sectionId => this.runSectionAiAnalysis(sectionId, { force: true })));
        this.$message.success('所有章节分析完成');
      } catch (error) {
        console.error('重新分析所有章节失败:', error);
        this.$message.error('重新分析失败，请检查网络连接');
      } finally {
        this.isRerunningAll = false;
      }
    },

    // 仅尝试从后端读取历史缓存结果（不触发新的AI调用）
    async loadCachedAiResults() {
      const sections = ['overview', 'structure', 'trend', 'volatility', 'growth', 'budget', 'forecast'];
      await Promise.all(sections.map(sectionId => this.loadCachedAiResultForSection(sectionId)));
    },
    async loadCachedAiResultForSection(sectionId) {
      try {
        const { indicatorId, period } = this.currentParams;
        if (!this.hasValidParams) return;
        // 直接读取后端最新一条结果（若有则展示），避免状态不匹配导致的漏显
        const resp = await getExpenseAnalysisResult({ indicatorId, period, analysisType: sectionId });
        if (resp?.data?.data) {
          this.parseAiAnalysisResult(sectionId, resp.data.data);
        }
      } catch (e) {
        console.warn('读取缓存AI结果失败：', sectionId, e);
      }
    },
    
    // 测试所有AI分析功能（已取消模拟数据注入，仅保留占位以防被调用）
    async testAllAiAnalysis() {
      this.$message.info('测试模式已关闭，暂不注入模拟AI结果');
    },
    
    // 使用模拟AI分析数据（禁用）
    useMockAiAnalysis(sectionId) {
      console.warn('useMockAiAnalysis 已禁用，不再注入模拟结果');
      return;
      /*
      const mockResults = {
        overview: `<think>
分析数据概览的关键要点：
1. 当前月份费用与预算的对比
2. 预算执行率评估
3. Z分数异常检测
4. 费用趋势分析
</think>

## 数据概览分析结果

### 📊 当前月份状况
- **本月费用**: ¥${this.formatNumber(this.kpis?.currentMonthExpense || 0)}
- **预算执行率**: ${((this.kpis?.budgetExecutionRate || 1) * 100).toFixed(1)}%
- **Z分数**: ${(this.kpis?.currentMonthZScore || 0).toFixed(2)}

### ⚠️ 关键发现
${this.kpis?.budgetExecutionRate > 1.1 ? '当前预算执行率过高，存在超支风险，建议加强成本控制。' : this.kpis?.budgetExecutionRate < 0.9 ? '当前预算执行率偏低，可能存在预算浪费，建议优化资源配置。' : '当前预算执行情况良好，继续保持。'}

### 💡 建议措施
1. 持续监控费用趋势变化
2. 建立预警机制，及时应对异常情况
3. 定期评估预算执行情况，优化资源配置`,

        structure: `<think>
分析费用结构的关键要点：
1. 主要费用类别识别
2. 占比分布分析
3. 优化机会识别
4. 成本控制建议
</think>

## 费用结构分析结果

### 📈 结构概况
${this.structure?.rows && this.structure.rows.length > 0 ? 
  `主要费用集中在「${this.structure.rows[0]?.name || '未知类别'}」，占比 ${((this.structure.rows[0]?.ratio || 0) * 100).toFixed(1)}%` : 
  '暂无结构数据'}

### 🎯 优化建议
1. 对Top3费用类别进行重点监控
2. 实施集中采购，降低采购成本
3. 建立费用标准，规范支出行为
4. 定期评估费用结构，优化资源配置`,

        trend: `<think>
分析费用趋势的关键要点：
1. 趋势方向判断
2. 拐点识别
3. 季节性分析
4. 异常点检测
</think>

## 费用趋势分析结果

### 📊 趋势概况
${this.trend?.indicators ? 
  `当前费用呈现${this.trend.indicators.trendDirection || '平稳'}趋势，Z分数为 ${this.trend.indicators.currentZScore?.toFixed(2) || '0.00'}` : 
  '暂无趋势数据'}

### 🔍 关键发现
1. 趋势变化符合预期
2. 无明显异常波动
3. 季节性特征明显

### 💡 建议措施
1. 继续监控趋势变化
2. 建立趋势预警机制
3. 根据趋势调整预算分配`,

        volatility: `<think>
分析波动性的关键要点：
1. 波动幅度评估
2. 异常检测
3. 风险评估
4. 控制措施
</think>

## 波动性分析结果

### 📊 波动概况
${this.volatility?.indicators ? 
  `当前波动状态：${this.volatility.indicators.volatilityStatus === 'normal' ? '正常' : this.volatility.indicators.volatilityStatus === 'moderate' ? '较大' : '异常'}` : 
  '暂无波动性数据'}

### ⚠️ 风险评估
1. 波动幅度在可控范围内
2. 无明显异常波动
3. 风险等级较低

### 💡 建议措施
1. 继续监控波动情况
2. 建立波动预警机制
3. 优化成本控制措施`,

        growth: `<think>
分析增长率的关键要点：
1. 环比同比分析
2. 增长驱动因素
3. 异常增长识别
4. 增长预测
</think>

## 增长率分析结果

### 📈 增长概况
${this.growth?.indicators ? 
  `当前环比增长率：${(this.growth.indicators.currentMomGrowth * 100).toFixed(1)}%，增长率状态：${this.growth.indicators.growthStatus === 'normal' ? '正常' : this.growth.indicators.growthStatus === 'moderate' ? '较大' : '异常'}` : 
  '暂无增长率数据'}

### 🔍 关键发现
1. 增长率符合预期
2. 无明显异常增长
3. 增长趋势稳定

### 💡 建议措施
1. 继续监控增长率变化
2. 建立增长预警机制
3. 优化增长策略`,

        budget: `<think>
分析预算执行的关键要点：
1. 预算执行率评估
2. 偏差分析
3. 风险评估
4. 改进建议
</think>

## 预算分析结果

### 📊 执行概况
${this.budget?.indicators ? 
  `当月预算执行率：${(this.budget.indicators.currentMonthExecutionRate * 100).toFixed(1)}%，风险等级：${this.budget.budgetRisk?.riskLevel === 'high' ? '高风险' : this.budget.budgetRisk?.riskLevel === 'medium' ? '中等风险' : '低风险'}` : 
  '暂无预算数据'}

### ⚠️ 关键发现
1. 预算执行情况良好
2. 偏差在可控范围内
3. 风险等级较低

### 💡 建议措施
1. 继续加强预算管控
2. 建立预算预警机制
3. 优化预算分配策略`,

        forecast: `<think>
分析预测结果的关键要点：
1. 预测准确性评估
2. 趋势预测分析
3. 风险预警
4. 应对策略
</think>

## 预测分析结果

### 🔮 预测概况
${this.forecast?.indicators ? 
  `预测准确度：${(this.forecast.indicators.forecastAccuracy * 100).toFixed(1)}%，置信度：${(this.forecast.indicators.confidenceLevel * 100).toFixed(1)}%` : 
  '暂无预测数据'}

### 📊 关键发现
1. 预测模型表现良好
2. 预测结果可信度高
3. 趋势预测准确

### 💡 建议措施
1. 继续优化预测模型
2. 根据预测结果调整策略
3. 建立预测监控机制`
      };
      
      const mockResult = mockResults[sectionId] || '';
      this.parseAiAnalysisResult(sectionId, mockResult);
      */
    },
    
    // 测试ECharts基本功能
    testECharts() {
      console.log('=== 测试ECharts基本功能 ===');
      let el = this.$refs[this.refs.category];
      console.log('原始容器引用:', el);
      
      // 处理refs可能返回数组的情况
      if (Array.isArray(el)) {
        console.log('容器是数组，取第一个元素');
        el = el[0];
      }
      
      console.log('处理后的容器:', el);
      
      if (!el) {
        console.error('容器未找到');
        return;
      }
      
      // 检查是否为有效的DOM元素
      if (!el.tagName || typeof el.getBoundingClientRect !== 'function') {
        console.error('容器不是有效的DOM元素:', el);
        return;
      }
      
      try {
        // 销毁现有图表
        if (this.charts.category) {
          this.charts.category.dispose();
        }
        
        // 创建测试图表
        const chart = echarts.init(el);
        this.charts.category = chart;
        
        const testOption = {
          title: {
            text: 'ECharts测试',
            left: 'center'
          },
          series: [{
            type: 'pie',
            radius: '50%',
            data: [
              { name: '测试1', value: 100 },
              { name: '测试2', value: 200 },
              { name: '测试3', value: 300 }
            ]
          }]
        };
        
        chart.setOption(testOption);
        console.log('✅ ECharts测试成功');
        
      } catch (error) {
        console.error('❌ ECharts测试失败:', error);
      }
    },
    handleResize() {
      Object.values(this.charts).forEach(c => c && c.resize());
    },
    scrollToSection(id) {
      try {
        const sc = this.$refs.scrollContainer;
        const el = document.getElementById(id);
        if (!sc || !el) return;
        this.tocVisible = false;
        const scRect = sc.$el ? sc.$el.getBoundingClientRect() : sc.getBoundingClientRect?.() || sc;
        const elRect = el.getBoundingClientRect();
        const delta = elRect.top - scRect.top - 8; // 适当留白
        const target = (sc.$el || sc).scrollTop + delta;
        (sc.$el || sc).scrollTo({ top: target, behavior: 'smooth' });
        this.activeSection = id;
      } catch {}
    },
    onScroll() {
      // 根据容器内滚动位置高亮目录
      const sc = this.$refs.scrollContainer;
      const container = sc?.$el || sc;
      if (!container) return;
      const cTop = container.getBoundingClientRect ? container.getBoundingClientRect().top : 0;
      const offsets = this.sections.map(s => {
        const el = document.getElementById(s.id);
        const rect = el ? el.getBoundingClientRect() : null;
        return { id: s.id, top: rect ? rect.top - cTop : Infinity };
      }).filter(x => x.top !== Infinity);
      const threshold = 80;
      const visible = offsets.filter(o => o.top - threshold <= 0);
      if (visible.length > 0) {
        const current = visible.sort((a,b)=>b.top-a.top)[0];
        this.activeSection = current.id;
      }
    },
    computeKpisAndConclusions() {
      const total = this.seriesMonthly.reduce((a,b)=>a+b,0);
      const avg = total / this.seriesMonthly.length;
      const std = Math.sqrt(this.seriesMonthly.map(v => Math.pow(v-avg,2)).reduce((a,b)=>a+b,0)/this.seriesMonthly.length);
      const cv = std / avg;
      const zScores = this.seriesMonthly.map(v => (v-avg)/std);
      const anoms = zScores.filter(z => Math.abs(z) > 2.5).length;
      
      // 计算当前月份相关指标
      const currentMonthValue = this.seriesMonthly[this.seriesMonthly.length - 1];
      const previousMonthValue = this.seriesMonthly[this.seriesMonthly.length - 2] || currentMonthValue;
      const currentMonthTrend = previousMonthValue > 0 ? ((currentMonthValue - previousMonthValue) / previousMonthValue) * 100 : 0;
      const currentMonthZScore = zScores[zScores.length - 1];
      
      // 计算当前月份排名
      const sortedValues = [...this.seriesMonthly].sort((a, b) => b - a);
      const currentMonthRank = sortedValues.indexOf(currentMonthValue) + 1;
      const currentMonthPercentile = Math.round((currentMonthRank / this.seriesMonthly.length) * 100);
      
      // 设置当前月份
      this.currentMonth = this.months[this.months.length - 1] || '2024-06';
      
      this.kpis = { 
        totalExpense: total, 
        avgMonthly: avg, 
        cv, 
        anomalyMonths: anoms,
        // 当前月份相关指标
        currentMonthExpense: currentMonthValue,
        currentMonthTrend: currentMonthTrend,
        budgetExecutionRate: 1.05, // 模拟预算执行率
        currentMonthBudget: Math.round(currentMonthValue * 0.95), // 模拟预算金额
        currentMonthZScore: currentMonthZScore,
        currentMonthRank: currentMonthRank,
        totalMonths: this.seriesMonthly.length,
        currentMonthPercentile: currentMonthPercentile
      };

      this.conclusions.overview = [
        `当前月份(${this.currentMonth})费用 ¥${this.formatNumber(currentMonthValue)}，${currentMonthTrend >= 0 ? '环比增长' : '环比下降'} ${Math.abs(currentMonthTrend).toFixed(1)}%`,
        `预算执行率 ${(this.kpis.budgetExecutionRate * 100).toFixed(1)}%，${this.kpis.budgetExecutionRate > 1.1 ? '存在超支风险' : this.kpis.budgetExecutionRate < 0.9 ? '预算执行不足' : '执行情况良好'}`,
        `Z分数 ${currentMonthZScore.toFixed(2)}，在历史数据中排名第${currentMonthRank}位(前${currentMonthPercentile}%)`,
        `分析期总费用约 ¥${this.formatNumber(total)}，月均 ¥${this.formatNumber(avg.toFixed(0))}，波动系数 ${(cv*100).toFixed(1)}%`
      ];
      const maxVal = Math.max(...this.seriesMonthly); const maxIdx = this.seriesMonthly.indexOf(maxVal);
      const maxMonth = this.months[maxIdx];
      this.conclusions.trend = [
        `整体趋势呈${this.seriesMonthly[this.seriesMonthly.length-1] - this.seriesMonthly[0] >= 0 ? '上升' : '下降'}态势，存在季节性波动`,
        `峰值出现在 ${maxMonth}（¥${this.formatNumber(maxVal)}）`
      ];
      // 结构分析结论将在loadStructure方法中通过updateStructureConclusions更新
      // 这里先设置为空，等待API数据加载后更新
      this.conclusions.structure = ['正在加载结构分析数据...'];
      this.conclusions.volatility = [
        `波动系数(CV)为 ${(cv*100).toFixed(1)}%，${cv>0.3?'波动较大':'波动可控'}`,
        `峰值波动与异常点需要联动审批与复盘`
      ];
      this.conclusions.growth = [
        `环比/同比增速需结合业务节奏进行解释`,
        `建议建立月度增速阈值告警`
      ];
      this.conclusions.forecast = [
        `基于历史数据AI预测未来趋势，结合预算数据进行比对分析`,
        `建议根据预测结果调整预算分配，优化资源配置`
      ];
      const risk = this.budgetVsActual.filter(x=>x.actual>x.budget).length;
      this.conclusions.budget = [
        `存在 ${risk} 个超预算月份，建议动态调整预算或分解责任`,
        `建立月度滚动预测与预警阈值（如偏差>15%）`
      ];
      this.conclusions.budget = [
        `存在 ${risk} 个超预算月份，建议动态调整预算或分解责任`,
        `建立月度滚动预测与预警阈值（如偏差>15%）`
      ];
      this.conclusions.advice = [
        `基于近12月趋势，下一周期建议控制在预算±10%-15%内`,
        `建议：强化预算闭环、优化集采、推进线上化与制度化降本`
      ];
    },
    updateStructureConclusions() {
      // 使用真实的API数据更新结构分析结论
      const rows = this.structure?.rows || [];
      if (rows.length === 0) {
        this.conclusions.structure = ['暂无结构数据'];
        return;
      }

      // 按金额排序，找出主要费用类别
      const sortedRows = [...rows].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));
      const topCategory = sortedRows[0];
      const totalAmount = rows.reduce((sum, row) => sum + parseFloat(row.amount || 0), 0);
      
      if (topCategory && totalAmount > 0) {
        const topRatio = ((parseFloat(topCategory.amount) / totalAmount) * 100).toFixed(1);
        this.conclusions.structure = [
          `主要费用集中在「${topCategory.name}」，占比 ${topRatio}%`,
          `建议对Top3类别进行重点监控与集采优化`
        ];
      } else {
        this.conclusions.structure = ['费用结构分析数据异常'];
      }
    },
    initTrend() {
      try {
        console.log('=== 开始初始化趋势图表 ===');
        
        // 1. 获取容器
        let el = this.$refs[this.refs.trend];
        console.log('1. 原始容器引用:', el);
        
        // 处理refs可能返回数组的情况
        if (Array.isArray(el)) {
          console.log('2. 容器是数组，取第一个元素');
          el = el[0];
        }
        
        console.log('3. 处理后的容器:', el);
        
        if (!el) {
          console.error('❌ 趋势图表容器未找到');
          return;
        }
        
        // 检查是否为有效的DOM元素
        if (!el.tagName || typeof el.getBoundingClientRect !== 'function') {
          console.error('❌ 容器不是有效的DOM元素:', el);
          return;
        }
        
        // 2. 检查容器尺寸
        const rect = el.getBoundingClientRect();
        console.log('4. 容器尺寸:', rect);
        
        // 3. 销毁现有图表
        if (this.charts.trend) {
          console.log('5. 销毁现有趋势图表');
          this.charts.trend.dispose();
        }
        
        // 4. 初始化ECharts
        console.log('6. 初始化ECharts实例');
        const chart = echarts.init(el);
        this.charts.trend = chart;
        console.log('7. ECharts实例创建成功:', chart);
        
        // 5. 准备数据
        console.log('8. 趋势数据:', this.trend);
        
        if (!this.trend.monthlyData || this.trend.monthlyData.length === 0) {
          console.log('9. 趋势数据为空，显示暂无数据');
          chart.setOption({
            title: { 
              text: '暂无趋势数据', 
              left: 'center', 
              top: 'middle',
              textStyle: { color: '#999', fontSize: 14 }
            }
          });
          return;
        }
        
        // 6. 创建ECharts配置
        const monthlyData = this.trend.monthlyData;
        const months = monthlyData.map(item => item.month);
        const values = monthlyData.map(item => item.value);
        const isCurrentMonth = monthlyData.map(item => item.isCurrentMonth);
        const { trendLimits } = this.trend;
        
        console.log('10. 月度数据:', monthlyData);
        console.log('11. 月份标签:', months);
        console.log('12. 费用值:', values);
        console.log('13. 当前月份标记:', isCurrentMonth);
        console.log('14. 趋势控制限:', trendLimits);
        
        const option = {
          title: {
            text: '费用趋势分析',
            left: 'center',
            top: 10,
            textStyle: { fontSize: 14, color: '#333' }
          },
          tooltip: { 
            trigger: 'axis',
            formatter: function(params) {
              let result = params[0].axisValue + '<br/>';
              params.forEach(param => {
                const value = param.value.toLocaleString();
                const isCurrent = isCurrentMonth[params[0].dataIndex];
                const marker = isCurrent ? '🔴' : param.marker;
                result += `${marker}${param.seriesName}: ¥${value}${isCurrent ? ' (当前月)' : ''}<br/>`;
              });
              return result;
            }
          },
          legend: { 
            top: 35,
            left: 'center'
          },
          grid: { 
            left: 50, 
            right: 30, 
            top: 70, 
            bottom: 50 
          },
          xAxis: { 
            type: 'category', 
            data: months,
            axisLabel: { fontSize: 12 }
          },
          yAxis: { 
            type: 'value', 
            axisLabel: { 
              formatter: val => `¥${(val/1000)}k`,
              fontSize: 12
            }
          },
          series: [
            {
              name: '费用金额',
              type: 'line',
              data: values,
              symbolSize: function(value, params) {
                return isCurrentMonth[params.dataIndex] ? 10 : 6; // 高亮当前月份
              },
              itemStyle: function(params) {
                return {
                  color: isCurrentMonth[params.dataIndex] ? '#f56c6c' : '#409eff' // 当前月份用红色
                };
              },
              lineStyle: { width: 2 }
            },
            {
              name: '历史均值',
              type: 'line',
              data: months.map(() => trendLimits.historicalMean),
              lineStyle: { type: 'dashed', color: '#67c23a', width: 2 }
            },
            {
              name: '+2σ',
              type: 'line',
              data: months.map(() => trendLimits.upper2Sigma),
              lineStyle: { type: 'dashed', color: '#e6a23c' }
            },
            {
              name: '-2σ',
              type: 'line',
              data: months.map(() => trendLimits.lower2Sigma),
              lineStyle: { type: 'dashed', color: '#e6a23c' }
            },
            {
              name: '+3σ',
              type: 'line',
              data: months.map(() => trendLimits.upper3Sigma),
              lineStyle: { type: 'dotted', color: '#f56c6c' }
            },
            {
              name: '-3σ',
              type: 'line',
              data: months.map(() => trendLimits.lower3Sigma),
              lineStyle: { type: 'dotted', color: '#f56c6c' }
            }
          ]
        };
        
        console.log('13. ECharts配置:', option);
        
        // 7. 设置配置
        chart.setOption(option);
        console.log('14. ✅ 趋势图表配置设置成功');
        
        // 8. 验证图表是否渲染
        setTimeout(() => {
          const canvas = el.querySelector('canvas');
          console.log('15. 检查canvas元素:', canvas);
          if (canvas) {
            console.log('16. ✅ Canvas元素存在，趋势图表应该已渲染');
          } else {
            console.warn('17. ⚠️ Canvas元素未找到，趋势图表可能未渲染');
          }
        }, 100);
        
      } catch (error) {
        console.error('❌ 趋势图表初始化失败:', error);
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          trend: this.trend,
          refs: this.$refs
        });
      }
    },
    async loadTrendData() {
      try {
        this.trendLoading = true;
        
        console.log('=== loadTrendData 开始 ===');
        console.log('this.indicatorId:', this.indicatorId);
        console.log('this.period:', this.period);
        
        // 获取指标ID，支持多种方式传入
        let indicatorId = this.indicatorId;
        if (!indicatorId && this.$attrs.indicatorId) {
          indicatorId = this.$attrs.indicatorId;
        }
        if (!indicatorId && this.$attrs.indicator_id) {
          indicatorId = this.$attrs.indicator_id;
        }
        if (!indicatorId && this.$route.query.indicatorId) {
          indicatorId = parseInt(this.$route.query.indicatorId);
        }
        if (!indicatorId && this.$route.query.indicator_id) {
          indicatorId = parseInt(this.$route.query.indicator_id);
        }
        
        // 获取期间，支持多种方式传入
        let period = this.period;
        if (!period && this.$attrs.period) {
          period = this.$attrs.period;
        }
        if (!period && this.$route.query.period) {
          period = this.$route.query.period;
        }
        if (!period && this.months.length > 0) {
          period = this.months[this.months.length - 1];
        }
        
        console.log('最终参数:', { indicatorId, period });

        if (!indicatorId || indicatorId === 0) {
          console.warn('缺少指标ID参数，当前值:', indicatorId);
          this.$message.warning('缺少指标ID参数，无法加载趋势数据');
          return;
        }
        
        if (!period || period.trim() === '') {
          console.warn('缺少期间参数，当前值:', period);
          this.$message.warning('缺少期间参数，无法加载趋势数据');
          return;
        }

        console.log('开始调用趋势对比API，参数:', { indicatorId, period });
        const response = await getTrendComparison(indicatorId, period);
        console.log('趋势对比API响应:', response);

        if (response?.data?.data) {
          this.trend = response.data.data;
          console.log('设置趋势数据:', this.trend);
        } else if (response?.data?.msg) {
          // 处理业务错误
          console.warn('业务错误:', response.data.msg);
          this.$message.warning(response.data.msg);
          return;
        } else {
          console.warn('接口返回数据格式异常:', response);
          this.$message.warning('接口返回数据格式异常，请检查后端服务');
          return;
        }
        
        console.log('设置趋势数据:', this.trend);
        
        // 更新趋势分析结论
        this.updateTrendConclusions();

        // 标记趋势数据就绪并刷新概览KPI
        this.overviewKpiReady.trend = true;
        this.updateOverviewKpisFromApis();
        
        this.$nextTick(() => {
          this.initTrend();
        });
        
      } catch (error) {
        console.error('loadTrendData error:', error);
        let errorMsg = '加载趋势数据失败';
        if (error.response?.data?.msg) {
          errorMsg += '：' + error.response.data.msg;
        } else if (error.message) {
          errorMsg += '：' + error.message;
        }
        this.$message.error(errorMsg);
      } finally {
        this.trendLoading = false;
        console.log('=== loadTrendData 结束 ===');
      }
    },
    async loadVolatilityData() {
      this.volatilityLoading = true;
      try {
        console.log('=== loadVolatilityData 开始 ===');
        
        const { indicatorId, period } = this.currentParams;
        
        if (!this.hasValidParams) {
          console.warn('参数不完整，跳过波动性数据加载');
          this.$message.warning('请先设置指标ID和期间参数');
          return;
        }

        console.log('开始调用波动性分析API，参数:', { indicatorId, period });
        const response = await getVolatilityAnalysis(indicatorId, period);
        console.log('波动性分析API响应:', response);

        if (response?.data?.data) {
          this.volatility = response.data.data;
          console.log('设置波动性数据:', this.volatility);
          this.updateVolatilityConclusions();
          
          // 数据加载完成后，重新初始化波动性图表
          this.$nextTick(() => {
            console.log('波动性数据加载完成，重新初始化图表');
            this.initVolatility();
          });
        } else if (response?.data?.msg) {
          // 处理业务错误
          console.warn('业务错误:', response.data.msg);
          this.$message.warning(response.data.msg);
          return;
        } else {
          console.warn('接口返回数据格式异常:', response);
          this.$message.warning('接口返回数据格式异常，请检查后端服务');
          return;
        }
        
        console.log('设置波动性数据:', this.volatility);
      } catch (error) {
        console.error('loadVolatilityData error:', error);
        let errorMsg = '加载波动性数据失败';
        if (error.response?.data?.msg) {
          errorMsg += '：' + error.response.data.msg;
        } else if (error.message) {
          errorMsg += '：' + error.message;
        }
        this.$message.error(errorMsg);
      } finally {
        this.volatilityLoading = false;
        console.log('=== loadVolatilityData 结束 ===');
      }
    },
    async loadGrowthData() {
      this.growthLoading = true;
      try {
        console.log('=== loadGrowthData 开始 ===');
        
        const { indicatorId, period } = this.currentParams;
        
        if (!this.hasValidParams) {
          console.warn('参数不完整，跳过增长率数据加载');
          this.$message.warning('请先设置指标ID和期间参数');
          return;
        }

        console.log('开始调用增长率分析API，参数:', { indicatorId, period });
        const response = await getGrowthAnalysis(indicatorId, period);
        console.log('增长率分析API响应:', response);

        if (response?.data?.data) {
          this.growth = response.data.data;
          console.log('设置增长率数据:', this.growth);
          this.updateGrowthConclusions();
          
          // 数据加载完成后，重新初始化增长率图表
          this.$nextTick(() => {
            console.log('增长率数据加载完成，重新初始化图表');
            this.initCorr();
          });
        } else if (response?.data?.msg) {
          // 处理业务错误
          console.warn('业务错误:', response.data.msg);
          this.$message.warning(response.data.msg);
          return;
        } else {
          console.warn('接口返回数据格式异常:', response);
          this.$message.warning('接口返回数据格式异常，请检查后端服务');
          return;
        }
        
        console.log('设置增长率数据:', this.growth);
      } catch (error) {
        console.error('loadGrowthData error:', error);
        let errorMsg = '加载增长率数据失败';
        if (error.response?.data?.msg) {
          errorMsg += '：' + error.response.data.msg;
        } else if (error.message) {
          errorMsg += '：' + error.message;
        }
        this.$message.error(errorMsg);
      } finally {
        this.growthLoading = false;
        console.log('=== loadGrowthData 结束 ===');
      }
    },
    async loadBudgetData() {
      this.budgetLoading = true;
      try {
        console.log('=== loadBudgetData 开始 ===');
        
        const { indicatorId, period } = this.currentParams;
        
        if (!this.hasValidParams) {
          console.warn('参数不完整，跳过预算数据加载');
          this.$message.warning('请先设置指标ID和期间参数');
          return;
        }

        console.log('开始调用预算分析API，参数:', { indicatorId, period });
        const response = await getBudgetAnalysis(indicatorId, period);
        console.log('预算分析API响应:', response);

        if (response?.data?.data) {
          this.budget = response.data.data;
          console.log('设置预算数据:', this.budget);
          this.updateBudgetConclusions();
          
          // 数据加载完成后，重新初始化预算图表
          this.$nextTick(() => {
            console.log('预算数据加载完成，重新初始化图表');
            this.initBudget();
          });
          // 标记预算数据就绪并刷新概览KPI
          this.overviewKpiReady.budget = true;
          this.updateOverviewKpisFromApis();
        } else if (response?.data?.msg) {
          // 处理业务错误
          console.warn('业务错误:', response.data.msg);
          this.$message.warning(response.data.msg);
          return;
        } else {
          console.warn('接口返回数据格式异常:', response);
          this.$message.warning('接口返回数据格式异常，请检查后端服务');
          return;
        }
        
        console.log('设置预算数据:', this.budget);
      } catch (error) {
        console.error('loadBudgetData error:', error);
        let errorMsg = '加载预算数据失败';
        if (error.response?.data?.msg) {
          errorMsg += '：' + error.response.data.msg;
        } else if (error.message) {
          errorMsg += '：' + error.message;
        }
        this.$message.error(errorMsg);
      } finally {
        this.budgetLoading = false;
        console.log('=== loadBudgetData 结束 ===');
      }
    },
    async loadForecastData() {
      this.forecastLoading = true;
      try {
        console.log('=== loadForecastData 开始 ===');
        
        const { indicatorId, period } = this.currentParams;
        
        if (!this.hasValidParams) {
          console.warn('参数不完整，跳过预测数据加载');
          this.$message.warning('请先设置指标ID和期间参数');
          return;
        }

        console.log('开始调用预测分析API，参数:', { indicatorId, period });
        const response = await getForecastAnalysis(indicatorId, period);
        console.log('预测分析API响应:', response);

        if (response?.data?.data) {
          this.forecast = response.data.data;
          console.log('设置预测数据:', this.forecast);
          this.updateForecastConclusions();
          
          // 数据加载完成后，重新初始化预测图表和AI分析
          this.$nextTick(() => {
            console.log('预测数据加载完成，重新初始化图表');
            this.initForecast();
          });
        } else if (response?.data?.msg) {
          // 处理业务错误
          console.warn('业务错误:', response.data.msg);
          this.$message.warning(response.data.msg);
          return;
        } else {
          console.warn('接口返回数据格式异常:', response);
          this.$message.warning('接口返回数据格式异常，请检查后端服务');
          return;
        }
        
        console.log('设置预测数据:', this.forecast);
      } catch (error) {
        console.error('loadForecastData error:', error);
        let errorMsg = '加载预测数据失败';
        if (error.response?.data?.msg) {
          errorMsg += '：' + error.response.data.msg;
        } else if (error.message) {
          errorMsg += '：' + error.message;
        }
        this.$message.error(errorMsg);
      } finally {
        this.forecastLoading = false;
        console.log('=== loadForecastData 结束 ===');
      }
    },
    computeTrendIndicators(trendData) {
      const currentYearTotal = trendData.currentYear.reduce((sum, item) => sum + item.value, 0);
      const lastYearTotal = trendData.lastYear.reduce((sum, item) => sum + item.value, 0);
      const growthRate = lastYearTotal > 0 ? ((currentYearTotal - lastYearTotal) / lastYearTotal * 100) : 0;
      const avgMonthly = currentYearTotal / trendData.currentYear.length;
      
      const maxMonthData = trendData.currentYear.reduce((max, item) => 
        item.value > max.value ? item : max, trendData.currentYear[0]);
      const minMonthData = trendData.currentYear.reduce((min, item) => 
        item.value < min.value ? item : min, trendData.currentYear[0]);
      
      return {
        currentYearTotal,
        lastYearTotal,
        growthRate,
        avgMonthly,
        maxMonth: maxMonthData?.month || '',
        minMonth: minMonthData?.month || ''
      };
    },
    updateTrendConclusions() {
      const { indicators, trendAnalysis } = this.trend;
      const deviationPercent = (indicators.currentDeviationRate * 100).toFixed(1);
      const zScore = indicators.currentZScore.toFixed(2);
      
      // 趋势状态描述
      const statusText = {
        'normal': '正常',
        'moderate': '较大',
        'high': '异常'
      }[indicators.trendStatus] || '正常';
      
      // 趋势方向描述
      const directionText = indicators.trendDirection;
      
      // 历史排名描述
      const rankText = `${indicators.historicalRank}/11 (${indicators.percentile}%)`;
      
      this.conclusions.trend = [
        `${trendAnalysis.assessment}，Z分数为 ${zScore}`,
        `${trendAnalysis.directionDescription}，偏离率 ${deviationPercent}%`,
        `${trendAnalysis.amplitudeDescription}，在历史数据中排名 ${rankText}`,
        `${trendAnalysis.trendPersistence}`,
        `建议${indicators.trendStatus === 'normal' ? '继续保持' : indicators.trendStatus === 'moderate' ? '关注趋势变化' : '重点关注并分析原因'}`
      ];
    },
    updateVolatilityConclusions() {
      const { indicators, volatilityAnalysis } = this.volatility;
      const deviationPercent = (indicators.currentDeviationRate * 100).toFixed(1);
      const zScore = indicators.currentZScore.toFixed(2);
      
      // 波动状态描述
      const statusText = {
        'normal': '正常',
        'moderate': '较大',
        'high': '异常'
      }[indicators.volatilityStatus] || '正常';
      
      // 波动方向描述
      const directionText = indicators.volatilityDirection;
      const directionColor = indicators.volatilityDirection === '上升' ? '上升' : indicators.volatilityDirection === '下降' ? '下降' : '持平';
      
      // 历史排名描述
      const rankText = `${indicators.historicalRank}/11 (${indicators.percentile}%)`;
      
      this.conclusions.volatility = [
        `${volatilityAnalysis.assessment}，Z分数为 ${zScore}`,
        `${volatilityAnalysis.directionDescription}，偏离率 ${deviationPercent}%`,
        `${volatilityAnalysis.amplitudeDescription}，在历史数据中排名 ${rankText}`,
        `${volatilityAnalysis.seasonalAnalysis}`,
        `建议${indicators.volatilityStatus === 'normal' ? '继续保持' : indicators.volatilityStatus === 'moderate' ? '关注波动趋势' : '重点关注并分析原因'}`
      ];
    },
    updateGrowthConclusions() {
      const { indicators, growthAnalysis } = this.growth;
      const growthPercent = (indicators.currentMomGrowth * 100).toFixed(1);
      const deviationPercent = (indicators.currentMomDeviation * 100).toFixed(1);
      const zScore = indicators.currentMomZScore.toFixed(2);
      
      // 增长率状态描述
      const statusText = {
        'normal': '正常',
        'moderate': '较大',
        'high': '异常'
      }[indicators.growthStatus] || '正常';
      
      // 增长率方向描述
      const directionText = indicators.growthDirection;
      
      // 历史排名描述
      const rankText = `${indicators.historicalRank}/10 (${indicators.percentile}%)`;
      
      this.conclusions.growth = [
        `${growthAnalysis.assessment}，Z分数为 ${zScore}`,
        `${growthAnalysis.directionDescription}，增长率 ${growthPercent}%`,
        `${growthAnalysis.amplitudeDescription}，在历史数据中排名 ${rankText}`,
        `${growthAnalysis.growthTrend}`,
        `建议${indicators.growthStatus === 'normal' ? '继续保持' : indicators.growthStatus === 'moderate' ? '关注增长率变化' : '重点关注并分析原因'}`
      ];
    },
    updateBudgetConclusions() {
      const { indicators, budgetRisk } = this.budget;
      const currentMonthExecutionRatePercent = (indicators.currentMonthExecutionRate * 100).toFixed(1);
      const overallExecutionRatePercent = (indicators.budgetExecutionRate * 100).toFixed(1);
      const currentMonthVariancePercent = indicators.currentMonthBudget > 0 ? ((indicators.currentMonthVariance / indicators.currentMonthBudget) * 100).toFixed(1) : '0.0';
      
      // 风险等级描述
      const riskLevelText = {
        'high': '高风险',
        'medium': '中等风险',
        'low': '低风险'
      }[budgetRisk.riskLevel] || '低风险';
      
      // 当月预算执行情况描述
      let currentMonthStatus = '';
      if (indicators.currentMonthExecutionRate > 1.15) {
        currentMonthStatus = '当月预算执行率过高，存在严重超支风险';
      } else if (indicators.currentMonthExecutionRate > 1.05) {
        currentMonthStatus = '当月预算执行率偏高，需要关注';
      } else if (indicators.currentMonthExecutionRate < 0.85) {
        currentMonthStatus = '当月预算执行率偏低，可能存在预算浪费';
      } else {
        currentMonthStatus = '当月预算执行情况良好';
      }
      
      // 整体预算执行情况描述
      let overallStatus = '';
      if (indicators.budgetExecutionRate > 1.1) {
        overallStatus = '整体预算执行率过高，存在超支风险';
      } else if (indicators.budgetExecutionRate < 0.9) {
        overallStatus = '整体预算执行率过低，可能存在预算浪费';
      } else {
        overallStatus = '整体预算执行情况良好';
      }
      
      this.conclusions.budget = [
        `当月预算执行率 ${currentMonthExecutionRatePercent}%，${currentMonthStatus}`,
        `全年预算 ¥${this.formatNumber(indicators.annualBudget)}，月度预算 ¥${this.formatNumber(indicators.monthlyBudget)}`,
        `当月实际支出 ¥${this.formatNumber(indicators.currentMonthActual)}，偏差 ¥${this.formatNumber(indicators.currentMonthVariance)}（${currentMonthVariancePercent}%）`,
        `整体预算执行率 ${overallExecutionRatePercent}%，${overallStatus}`,
        `风险等级：${riskLevelText}（${budgetRisk.riskScore}分），主要风险：${budgetRisk.riskFactors.join('、')}`,
        `建议以全年预算为重要参考，加强当月预算管控，优化资源配置`
      ];
    },
    updateForecastConclusions() {
      const { indicators, forecastData } = this.forecast;
      const forecastAccuracyPercent = (indicators.forecastAccuracy * 100).toFixed(1);
      const confidenceLevelPercent = (indicators.confidenceLevel * 100).toFixed(1);
      const executionRatePercent = (indicators.currentMonthExecutionRate * 100).toFixed(1);
      
      // 预测方法描述
      const predictionMethodText = {
        'LinearRegression': '线性回归预测',
        'TimeSeries': '时间序列分析',
        'ARIMA': 'ARIMA模型',
        'NeuralNetwork': '神经网络预测',
        'Ensemble': '集成学习预测'
      }[forecastData.predictionMethod] || 'AI智能预测';
      
      // 预测准确度描述
      let accuracyStatus = '';
      if (indicators.forecastAccuracy >= 0.9) {
        accuracyStatus = '预测准确度很高，模型表现优秀';
      } else if (indicators.forecastAccuracy >= 0.8) {
        accuracyStatus = '预测准确度较高，模型表现良好';
      } else if (indicators.forecastAccuracy >= 0.7) {
        accuracyStatus = '预测准确度一般，需要优化模型';
      } else {
        accuracyStatus = '预测准确度较低，建议重新训练模型';
      }
      
      // 趋势分析
      let trendAnalysis = '';
      if (indicators.trendDirection === '上升') {
        trendAnalysis = '预测未来费用呈上升趋势，需要关注成本控制';
      } else if (indicators.trendDirection === '下降') {
        trendAnalysis = '预测未来费用呈下降趋势，成本控制效果良好';
      } else {
        trendAnalysis = '预测未来费用趋势平稳，保持现有管控措施';
      }
      
      // 预算执行分析
      let budgetAnalysis = '';
      if (indicators.currentMonthExecutionRate > 1.1) {
        budgetAnalysis = '当月预算执行率过高，存在超支风险';
      } else if (indicators.currentMonthExecutionRate < 0.9) {
        budgetAnalysis = '当月预算执行率偏低，可能存在预算浪费';
      } else {
        budgetAnalysis = '当月预算执行情况良好，在合理范围内';
      }
      
      this.conclusions.forecast = [
        `使用${predictionMethodText}进行费用预测，准确度 ${forecastAccuracyPercent}%，置信度 ${confidenceLevelPercent}%`,
        `${accuracyStatus}，${trendAnalysis}`,
        `当月预算执行率 ${executionRatePercent}%，${budgetAnalysis}`,
        `累计实际支出 ¥${this.formatNumber(indicators.totalActual)}，累计预算 ¥${this.formatNumber(indicators.totalBudget)}`,
        `建议根据预测结果调整预算分配，优化资源配置，提高预测准确性`
      ];
    },
    async loadStructure() {
      try {
        this.structureLoading = true;
        
        console.log('=== loadStructure 开始 ===');
        console.log('this.indicatorId:', this.indicatorId);
        console.log('this.period:', this.period);
        console.log('this.$attrs:', this.$attrs);
        console.log('this.$route.query:', this.$route.query);
        
        // 获取指标ID，支持多种方式传入
        let indicatorId = this.indicatorId;
        if (!indicatorId && this.$attrs.indicatorId) {
          indicatorId = this.$attrs.indicatorId;
          console.log('从 $attrs 获取 indicatorId:', indicatorId);
        }
        if (!indicatorId && this.$attrs.indicator_id) {
          indicatorId = this.$attrs.indicator_id;
          console.log('从 $attrs.indicator_id 获取 indicatorId:', indicatorId);
        }
        if (!indicatorId && this.$route.query.indicatorId) {
          indicatorId = parseInt(this.$route.query.indicatorId);
          console.log('从 URL 参数获取 indicatorId:', indicatorId);
        }
        if (!indicatorId && this.$route.query.indicator_id) {
          indicatorId = parseInt(this.$route.query.indicator_id);
          console.log('从 URL 参数 indicator_id 获取 indicatorId:', indicatorId);
        }
        
        // 获取期间，支持多种方式传入
        let period = this.period;
        if (!period && this.$attrs.period) {
          period = this.$attrs.period;
          console.log('从 $attrs 获取 period:', period);
        }
        if (!period && this.$route.query.period) {
          period = this.$route.query.period;
          console.log('从 URL 参数获取 period:', period);
        }
        if (!period && this.months.length > 0) {
          period = this.months[this.months.length - 1];
          console.log('使用默认期间:', period);
        }
        
        console.log('最终参数:', { indicatorId, period });

        if (!indicatorId || indicatorId === 0) {
          console.warn('缺少指标ID参数，当前值:', indicatorId);
          console.warn('可用的参数来源:', {
            props: this.indicatorId,
            attrs: this.$attrs,
            route: this.$route.query
          });
          this.$message.warning('缺少指标ID参数，无法加载结构数据');
          return;
        }
        
        if (!period || period.trim() === '') {
          console.warn('缺少期间参数，当前值:', period);
          this.$message.warning('缺少期间参数，无法加载结构数据');
          return;
        }

        console.log('开始调用API，参数:', { indicatorId, period });
        const response = await getCategoryStructure(indicatorId, period);
        console.log('API响应:', response);

        if (response?.data?.data) {
          this.structure = response.data.data;
          console.log('设置结构数据:', this.structure);
          
          // 更新结构分析结论
          this.updateStructureConclusions();
          
          this.$nextTick(() => {
            this.initStructurePie();
          });
        } else if (response?.data?.msg) {
          // 处理业务错误
          console.warn('业务错误:', response.data.msg);
          this.$message.warning(response.data.msg);
        } else {
          console.warn('接口返回数据格式异常:', response);
          this.$message.warning('接口返回数据格式异常，请检查后端服务');
        }
      } catch (error) {
        console.error('loadStructure error:', error);
        let errorMsg = '加载结构数据失败';
        if (error.response?.data?.msg) {
          errorMsg += '：' + error.response.data.msg;
        } else if (error.message) {
          errorMsg += '：' + error.message;
        }
        this.$message.error(errorMsg);
      } finally {
        this.structureLoading = false;
        console.log('=== loadStructure 结束 ===');
      }
    },
    initStructurePie() {
      try {
        console.log('=== 开始初始化饼图 ===');
        
        // 1. 获取容器
        let el = this.$refs[this.refs.category];
        console.log('1. 原始容器引用:', el);
        console.log('2. refs.category:', this.refs.category);
        console.log('3. 可用的refs:', Object.keys(this.$refs));
        
        // 处理refs可能返回数组的情况
        if (Array.isArray(el)) {
          console.log('4. 容器是数组，取第一个元素');
          el = el[0];
        }
        
        console.log('5. 处理后的容器:', el);
        
        if (!el) {
          console.error('❌ 饼图容器未找到');
          return;
        }
        
        // 检查是否为有效的DOM元素
        if (!el.tagName || typeof el.getBoundingClientRect !== 'function') {
          console.error('❌ 容器不是有效的DOM元素:', el);
          return;
        }
        
        // 2. 检查容器尺寸
        const rect = el.getBoundingClientRect();
        console.log('6. 容器尺寸:', rect);
        console.log('7. 容器样式:', {
          width: el.style.width,
          height: el.style.height,
          className: el.className
        });
        
        // 3. 销毁现有图表
        if (this.charts.category) {
          console.log('8. 销毁现有饼图');
          this.charts.category.dispose();
        }

        // 4. 初始化ECharts
        console.log('9. 初始化ECharts实例');
        const chart = echarts.init(el);
        this.charts.category = chart;
        console.log('10. ECharts实例创建成功:', chart);

        // 5. 准备数据
        console.log('11. 原始结构数据:', this.structure);
        const rows = this.structure?.rows || [];
        console.log('12. 数据行数:', rows.length);
        
        const pieData = rows.map((r, index) => {
          const item = {
            name: r.name || `类别${index + 1}`,
            value: parseFloat(r.amount) || 0
          };
          console.log(`13. 数据项${index + 1}:`, item);
          return item;
        }).filter(item => item.value > 0);

        console.log('14. 处理后的饼图数据:', pieData);

        if (pieData.length === 0) {
          console.log('15. 数据为空，显示暂无数据');
          chart.setOption({
            title: { 
              text: '暂无数据', 
              left: 'center', 
              top: 'middle',
              textStyle: { color: '#999', fontSize: 14 }
            }
          });
          return;
        }

        // 6. 计算总金额
        const totalAmount = pieData.reduce((sum, item) => sum + item.value, 0);
        console.log('16. 总金额:', totalAmount);

        // 7. 创建ECharts配置
        const option = {
          title: {
            text: `总金额: ¥${this.formatNumber(totalAmount)}`,
            left: 'center',
            top: 10,
            textStyle: { fontSize: 12, color: '#666' }
          },
          tooltip: { 
            trigger: 'item',
            formatter: function(params) {
              const ratio = ((params.value / totalAmount) * 100).toFixed(2);
              return `${params.name}<br/>金额: ¥${params.value.toLocaleString()}<br/>占比: ${ratio}%`;
            }
          },
          legend: { 
            orient: 'vertical',
            left: 'left',
            top: 'middle'
          },
          series: [{
            name: '费用结构',
            type: 'pie',
            radius: '50%',
            center: ['60%', '50%'],
            data: pieData,
            label: {
              show: true,
              formatter: function(params) {
                const ratio = ((params.value / totalAmount) * 100).toFixed(1);
                return `${params.name}\n${ratio}%`;
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        };
        
        console.log('17. ECharts配置:', option);
        
        // 8. 设置配置
        chart.setOption(option);
        console.log('18. ✅ 饼图配置设置成功');
        
        // 9. 验证图表是否渲染
        setTimeout(() => {
          const canvas = el.querySelector('canvas');
          console.log('19. 检查canvas元素:', canvas);
          if (canvas) {
            console.log('20. ✅ Canvas元素存在，饼图应该已渲染');
          } else {
            console.warn('21. ⚠️ Canvas元素未找到，饼图可能未渲染');
          }
        }, 100);
        
      } catch (error) {
        console.error('❌ 饼图初始化失败:', error);
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          structure: this.structure,
          refs: this.$refs
        });
      }
    },
    initDept() {
      try {
        console.log('initDept 开始');
        const el = this.$refs[this.refs.dept];
        console.log('部门图表容器:', el);
        
        if (!el) {
          console.warn('部门图表容器未找到，跳过初始化');
          return;
        }
        
        // 检查元素是否为有效的DOM元素
        if (!el.tagName) {
          console.warn('部门图表容器不是有效的DOM元素:', el);
          return;
        }
        
        console.log('部门图表容器类型:', el.tagName, '类名:', el.className);
        
        // 如果已存在图表，先销毁
        if (this.charts.dept) {
          this.charts.dept.dispose();
        }
        
        const chart = echarts.init(el);
        this.charts.dept = chart;
        
        chart.setOption({
          tooltip: { trigger: 'axis' },
          grid: { left: 60, right: 20, top: 30, bottom: 60 },
          xAxis: { type: 'category', data: this.deptSpend.map(d=>d.dept), axisLabel: { rotate: 20 } },
          yAxis: { type: 'value', axisLabel: { formatter: val => `¥${(val/1000)}k` } },
          series: [{ type: 'bar', data: this.deptSpend.map(d=>d.value), itemStyle: { color: '#5B8FF9' } }]
        });
        
        console.log('部门图表初始化成功');
      } catch (error) {
        console.error('部门图表初始化失败:', error);
      }
    },
    initVolatility() {
      try {
        console.log('=== initVolatility 开始 ===');
        console.log('1. 获取图表容器引用');
        let el = this.$refs[this.refs.anomaly]; // 使用同一个容器
        console.log('2. 原始容器引用:', el);
        
        // 处理refs可能返回数组的情况
        if (Array.isArray(el)) {
          console.log('3. 容器是数组，取第一个元素');
          el = el[0];
        }
        
        console.log('4. 处理后的容器:', el);
        
        if (!el) {
          console.warn('5. 波动性图表容器未找到，跳过初始化');
          return;
        }
        
        // 检查元素是否为有效的DOM元素
        if (!el.tagName || typeof el.getBoundingClientRect !== 'function') {
          console.warn('6. 波动性图表容器不是有效的DOM元素:', el);
          return;
        }
        
        console.log('7. 波动性图表容器类型:', el.tagName, '类名:', el.className);
        console.log('8. 容器尺寸:', el.getBoundingClientRect());
        
        // 如果已存在图表，先销毁
        if (this.charts.volatility) {
          console.log('9. 销毁现有图表');
          this.charts.volatility.dispose();
        }
        
        console.log('10. 初始化ECharts实例');
        const chart = echarts.init(el);
        this.charts.volatility = chart;
        console.log('11. ECharts实例创建成功:', chart);
        
        // 使用真实的波动性分析数据
        console.log('12. 获取波动性分析数据');
        const { monthlyData, indicators, controlLimits } = this.volatility;
        console.log('13. 波动性分析数据:', { monthlyData, indicators, controlLimits });
        
        if (!monthlyData || monthlyData.length === 0) {
          console.warn('14. 波动性数据为空，使用默认数据');
          
          chart.setOption({
            title: {
              text: '波动性分析图（默认数据）',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { trigger: 'axis' },
            legend: { top: 35, data: ['费用金额', '历史均值', '+2σ', '-2σ', '+3σ', '-3σ'] },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { type: 'category', data: this.months },
            yAxis: { type: 'value', axisLabel: { formatter: val => `¥${(val/1000)}k` } },
            series: [
              { name: '费用金额', type: 'line', data: this.seriesMonthly, symbolSize: 6 },
              { name: '历史均值', type: 'line', data: this.months.map(() => this.kpis.avgMonthly), lineStyle: { type: 'dashed', color: '#67c23a' } },
              { name: '+2σ', type: 'line', data: this.months.map(() => this.kpis.avgMonthly + 2 * this.kpis.cv), lineStyle: { type: 'dashed', color: '#e6a23c' } },
              { name: '-2σ', type: 'line', data: this.months.map(() => this.kpis.avgMonthly - 2 * this.kpis.cv), lineStyle: { type: 'dashed', color: '#e6a23c' } },
              { name: '+3σ', type: 'line', data: this.months.map(() => this.kpis.avgMonthly + 3 * this.kpis.cv), lineStyle: { type: 'dotted', color: '#f56c6c' } },
              { name: '-3σ', type: 'line', data: this.months.map(() => this.kpis.avgMonthly - 3 * this.kpis.cv), lineStyle: { type: 'dotted', color: '#f56c6c' } }
            ]
          });
        } else {
          console.log('15. 使用真实波动性分析数据');
          // 使用真实数据
          const months = monthlyData.map(item => item.month);
          const values = monthlyData.map(item => item.value);
          const isCurrentMonth = monthlyData.map(item => item.isCurrentMonth);
          
          console.log('16. 处理后的数据:', { months, values, isCurrentMonth, controlLimits });
          
          chart.setOption({
            title: {
              text: '波动性分析图',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { 
              trigger: 'axis',
              formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                  if (param.seriesName === '当前月份') {
                    result += `<span style="color:#f56c6c;font-weight:bold;">${param.marker}${param.seriesName}: ¥${(param.value/1000).toFixed(1)}k</span><br/>`;
                  } else {
                    result += param.marker + param.seriesName + ': ¥' + (param.value/1000).toFixed(1) + 'k<br/>';
                  }
                });
                return result;
              }
            },
            legend: { 
              top: 35,
              data: ['费用金额', '历史均值', '+2σ', '-2σ', '+3σ', '-3σ']
            },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { 
              type: 'category', 
              data: months,
              axisLabel: { rotate: 45 }
            },
            yAxis: { 
              type: 'value', 
              axisLabel: { formatter: val => `¥${(val/1000)}k` },
              name: '费用金额(千元)'
            },
            series: [
              { 
                name: '费用金额', 
                type: 'line', 
                data: values,
                symbolSize: function(value, params) {
                  return isCurrentMonth[params.dataIndex] ? 10 : 6;
                },
                itemStyle: function(params) {
                  return {
                    color: isCurrentMonth[params.dataIndex] ? '#f56c6c' : '#409eff'
                  };
                },
                lineStyle: { width: 2 }
              },
              { 
                name: '历史均值', 
                type: 'line', 
                data: months.map(() => controlLimits.historicalMean),
                lineStyle: { type: 'dashed', color: '#67c23a', width: 2 }
              },
              { 
                name: '+2σ', 
                type: 'line', 
                data: months.map(() => controlLimits.upper2Sigma),
                lineStyle: { type: 'dashed', color: '#e6a23c' }
              },
              { 
                name: '-2σ', 
                type: 'line', 
                data: months.map(() => controlLimits.lower2Sigma),
                lineStyle: { type: 'dashed', color: '#e6a23c' }
              },
              { 
                name: '+3σ', 
                type: 'line', 
                data: months.map(() => controlLimits.upper3Sigma),
                lineStyle: { type: 'dotted', color: '#f56c6c' }
              },
              { 
                name: '-3σ', 
                type: 'line', 
                data: months.map(() => controlLimits.lower3Sigma),
                lineStyle: { type: 'dotted', color: '#f56c6c' }
              }
            ]
          });
        }
        
        console.log('17. ✅ 波动性图表配置设置成功');
        
        // 验证图表是否渲染
        setTimeout(() => {
          const canvas = el.querySelector('canvas');
          console.log('18. 检查canvas元素:', canvas);
          if (canvas) {
            console.log('19. ✅ Canvas元素存在，波动性图表应该已渲染');
          } else {
            console.warn('20. ⚠️ Canvas元素未找到，波动性图表可能未渲染');
          }
        }, 100);
        
        console.log('=== initVolatility 结束 ===');
      } catch (error) {
        console.error('❌ 波动性图表初始化失败:', error);
        console.error('错误详情:', { message: error.message, stack: error.stack });
      }
    },
    initForecast() {
      try {
        console.log('=== initForecast 开始 ===');
        console.log('1. 获取图表容器引用');
        let el = this.$refs[this.refs.forecast];
        console.log('2. 原始容器引用:', el);
        
        // 处理refs可能返回数组的情况
        if (Array.isArray(el)) {
          console.log('3. 容器是数组，取第一个元素');
          el = el[0];
        }
        
        console.log('4. 处理后的容器:', el);
        
        if (!el) {
          console.warn('5. 预测图表容器未找到，跳过初始化');
          return;
        }
        
        // 检查元素是否为有效的DOM元素
        if (!el.tagName || typeof el.getBoundingClientRect !== 'function') {
          console.warn('6. 预测图表容器不是有效的DOM元素:', el);
          return;
        }
        
        console.log('7. 预测图表容器类型:', el.tagName, '类名:', el.className);
        console.log('8. 容器尺寸:', el.getBoundingClientRect());
        
        // 如果已存在图表，先销毁
        if (this.charts.forecast) {
          console.log('9. 销毁现有图表');
          this.charts.forecast.dispose();
        }
        
        console.log('10. 初始化ECharts实例');
        const chart = echarts.init(el);
        this.charts.forecast = chart;
        console.log('11. ECharts实例创建成功:', chart);
        
        // 使用真实的预测数据
        console.log('12. 获取预测数据');
        const { monthlyData, indicators, forecastData } = this.forecast;
        console.log('13. 预测数据:', { monthlyData, indicators, forecastData });
        
        if (!monthlyData || monthlyData.length === 0) {
          console.warn('14. 预测数据为空，使用默认数据');
          
          // 生成默认的预测数据
          const months = this.months;
          const historicalData = this.seriesMonthly;
          
          // 简单的线性回归预测
          const x = historicalData.map((_, i) => i);
          const y = historicalData;
          const n = x.length;
          const sumX = x.reduce((a, b) => a + b, 0);
          const sumY = y.reduce((a, b) => a + b, 0);
          const sumXY = x.reduce((s, xi, i) => s + xi * y[i], 0);
          const sumXX = x.reduce((s, xi) => s + xi * xi, 0);
          const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
          const intercept = (sumY - slope * sumX) / n;
          
          // 生成预测数据（未来6个月）
          const futureMonths = [];
          const predictedData = [];
          const budgetData = [];
          
          for (let i = 0; i < 12; i++) {
            const monthIndex = i + 1;
            const monthName = `${monthIndex}月`;
            futureMonths.push(monthName);
            
            if (i < historicalData.length) {
              // 历史数据
              predictedData.push(historicalData[i]);
              budgetData.push(historicalData[i] * 1.1); // 预算比实际高10%
            } else {
              // 预测数据
              const predictedValue = Math.round(slope * i + intercept);
              predictedData.push(predictedValue);
              budgetData.push(predictedValue * 1.1);
            }
          }
          
          console.log('15. 默认预测数据:', { futureMonths, predictedData, budgetData });
          
          chart.setOption({
            title: {
              text: '费用趋势预测分析（默认数据）',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { 
              trigger: 'axis',
              formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                  const v = Array.isArray(param.value) ? param.value[1] : param.value;
                  if (v == null || !isFinite(Number(v))) return;
                  const value = Number(v).toLocaleString();
                  result += param.marker + param.seriesName + ': ¥' + value + '<br/>';
                });
                return result;
              }
            },
            legend: { 
              top: 35,
              data: ['预算值', '实际累计', '预测累计']
            },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { 
              type: 'category', 
              data: futureMonths,
              axisLabel: { rotate: 45 }
            },
            yAxis: { 
              type: 'value', 
              axisLabel: { formatter: val => `¥${(val/1000)}k` },
              name: '费用金额(千元)'
            },
            series: [
              { 
                name: '预算值', 
                type: 'line', 
                data: budgetData,
                lineStyle: { type: 'dashed', color: '#5B8FF9', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '实际累计', 
                type: 'line', 
                data: predictedData.slice(0, historicalData.length),
                lineStyle: { color: '#FF6B6B', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '预测累计', 
                type: 'line', 
                data: predictedData.map((v, idx) => idx < historicalData.length ? null : v),
                lineStyle: { color: '#4ECDC4', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              }
            ]
          });
        } else {
          console.log('16. 使用真实预测数据');
          // 使用真实数据
          const { historicalData, predictedData, budgetData } = forecastData;
          
          // 合并历史数据和预测数据
          const allMonths = [];
          const allActualData = [];
          const allPredictedData = [];
          const allBudgetData = [];
          
          // 添加历史数据（预测曲线在历史段置为 null，后半段才显示）
          historicalData.forEach(item => {
            allMonths.push(item.month);
            allActualData.push(item.value);
            allPredictedData.push(null);
            allBudgetData.push(item.budget || item.value * 1.1);
          });
          
          // 添加预测数据
          predictedData.forEach(item => {
            allMonths.push(item.month);
            allActualData.push(null); // 未来月份没有实际值
            allPredictedData.push(item.value);
            allBudgetData.push(item.budget || item.value * 1.1);
          });
          
          console.log('17. 处理后的数据:', { allMonths, allActualData, allPredictedData, allBudgetData });
          
          chart.setOption({
            title: {
              text: '费用趋势预测分析',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { 
              trigger: 'axis',
              formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                  const v = Array.isArray(param.value) ? param.value[1] : param.value;
                  if (v == null || !isFinite(Number(v))) return;
                  const value = Number(v).toLocaleString();
                  result += param.marker + param.seriesName + ': ¥' + value + '<br/>';
                });
                return result;
              }
            },
            legend: { 
              top: 35,
              data: ['预算值', '实际累计', '预测累计']
            },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { 
              type: 'category', 
              data: allMonths,
              axisLabel: { rotate: 45 }
            },
            yAxis: { 
              type: 'value', 
              axisLabel: { formatter: val => `¥${(val/1000)}k` },
              name: '费用金额(千元)'
            },
            series: [
              { 
                name: '预算值', 
                type: 'line', 
                data: allBudgetData,
                lineStyle: { type: 'dashed', color: '#5B8FF9', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '实际累计', 
                type: 'line', 
                data: allActualData,
                lineStyle: { color: '#FF6B6B', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '预测累计', 
                type: 'line', 
                data: allPredictedData,
                lineStyle: { color: '#4ECDC4', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              }
            ]
          });
        }
        
        console.log('18. ✅ 预测图表配置设置成功');
        
        // 验证图表是否渲染
        setTimeout(() => {
          const canvas = el.querySelector('canvas');
          console.log('19. 检查canvas元素:', canvas);
          if (canvas) {
            console.log('20. ✅ Canvas元素存在，预测图表应该已渲染');
          } else {
            console.warn('21. ⚠️ Canvas元素未找到，预测图表可能未渲染');
          }
        }, 100);
        
        console.log('=== initForecast 结束 ===');
      } catch (error) {
        console.error('❌ 预测图表初始化失败:', error);
        console.error('错误详情:', { message: error.message, stack: error.stack });
      }
    },
    initBudget() {
      try {
        console.log('=== initBudget 开始 ===');
        console.log('1. 获取图表容器引用');
        let el = this.$refs[this.refs.budget];
        console.log('2. 原始容器引用:', el);
        
        // 处理refs可能返回数组的情况
        if (Array.isArray(el)) {
          console.log('3. 容器是数组，取第一个元素');
          el = el[0];
        }
        
        console.log('4. 处理后的容器:', el);
        
        if (!el) {
          console.warn('5. 预算图表容器未找到，跳过初始化');
          return;
        }
        
        // 检查元素是否为有效的DOM元素
        if (!el.tagName || typeof el.getBoundingClientRect !== 'function') {
          console.warn('6. 预算图表容器不是有效的DOM元素:', el);
          return;
        }
        
        console.log('7. 预算图表容器类型:', el.tagName, '类名:', el.className);
        console.log('8. 容器尺寸:', el.getBoundingClientRect());
        
        // 如果已存在图表，先销毁
        if (this.charts.budget) {
          console.log('9. 销毁现有图表');
          this.charts.budget.dispose();
        }
        
        console.log('10. 初始化ECharts实例');
        const chart = echarts.init(el);
        this.charts.budget = chart;
        console.log('11. ECharts实例创建成功:', chart);
        
        // 使用真实的预算分析数据
        console.log('12. 获取预算分析数据');
        const { monthlyData, indicators, budgetRisk } = this.budget;
        console.log('13. 预算分析数据:', { monthlyData, indicators, budgetRisk });
        
        if (!monthlyData || monthlyData.length === 0) {
          console.warn('14. 预算分析数据为空，使用默认数据');
          
          chart.setOption({
            title: {
              text: '预算分析图（默认数据）',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { trigger: 'axis' },
            legend: { top: 35, data: ['预算', '实际', '偏差'] },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { type: 'category', data: this.budgetVsActual.map(x=>x.month) },
            yAxis: { type: 'value', axisLabel: { formatter: val => `¥${(val/1000)}k` } },
            series: [
              { name: '预算', type: 'bar', data: this.budgetVsActual.map(x=>x.budget), itemStyle: { color: '#5AD8A6' } },
              { name: '实际', type: 'bar', data: this.budgetVsActual.map(x=>x.actual), itemStyle: { color: '#F6BD16' } },
              { name: '偏差', type: 'line', yAxisIndex: 0, data: this.budgetVsActual.map(x=>x.actual-x.budget),
                lineStyle: { color: '#F4664A' } }
            ]
          });
        } else {
          console.log('15. 使用真实预算分析数据');
          // 使用真实数据
          const months = monthlyData.map(item => item.month);
          const budgets = monthlyData.map(item => item.budget);
          const actuals = monthlyData.map(item => item.actual);
          const variances = monthlyData.map(item => item.variance);
          const isCurrentMonth = monthlyData.map(item => item.isCurrentMonth);
          
          console.log('16. 处理后的数据:', { months, budgets, actuals, variances, isCurrentMonth });
          
          chart.setOption({
            title: {
              text: '预算分析图',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { 
              trigger: 'axis',
              formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                const isCurrent = isCurrentMonth[params[0].dataIndex];
                if (isCurrent) {
                  result += '<span style="color:#f56c6c;font-weight:bold;">🔴 当前月份</span><br/>';
                }
                params.forEach(param => {
                  if (param.seriesName === '偏差') {
                    const value = param.value;
                    const color = value > 0 ? '#f56c6c' : '#67c23a';
                    result += `<span style="color:${color}">${param.marker}${param.seriesName}: ¥${(value/1000).toFixed(1)}k</span><br/>`;
                  } else {
                    result += param.marker + param.seriesName + ': ¥' + (param.value/1000).toFixed(1) + 'k<br/>';
                  }
                });
                return result;
              }
            },
            legend: { 
              top: 35,
              data: ['预算', '实际', '偏差']
            },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { 
              type: 'category', 
              data: months,
              axisLabel: { rotate: 45 }
            },
            yAxis: { 
              type: 'value', 
              axisLabel: { formatter: val => `¥${(val/1000)}k` },
              name: '金额(千元)'
            },
            series: [
              { 
                name: '预算', 
                type: 'bar', 
                data: budgets,
                itemStyle: function(params) {
                  return {
                    color: isCurrentMonth[params.dataIndex] ? '#1890ff' : '#5AD8A6'
                  };
                },
                barWidth: '40%'
              },
              { 
                name: '实际', 
                type: 'bar', 
                data: actuals,
                itemStyle: function(params) {
                  return {
                    color: isCurrentMonth[params.dataIndex] ? '#f56c6c' : '#F6BD16'
                  };
                },
                barWidth: '40%'
              },
              { 
                name: '偏差', 
                type: 'line', 
                data: variances,
                symbolSize: function(value, params) {
                  return isCurrentMonth[params.dataIndex] ? 10 : 6;
                },
                itemStyle: function(params) {
                  const value = params.value;
                  return {
                    color: isCurrentMonth[params.dataIndex] ? '#f56c6c' : (value > 0 ? '#f56c6c' : '#67c23a')
                  };
                },
                lineStyle: { 
                  color: '#F4664A',
                  width: 2
                }
              }
            ]
          });
        }
        
        console.log('17. ✅ 预算图表配置设置成功');
        
        // 验证图表是否渲染
        setTimeout(() => {
          const canvas = el.querySelector('canvas');
          console.log('18. 检查canvas元素:', canvas);
          if (canvas) {
            console.log('19. ✅ Canvas元素存在，预算图表应该已渲染');
          } else {
            console.warn('20. ⚠️ Canvas元素未找到，预算图表可能未渲染');
          }
        }, 100);
        
        console.log('=== initBudget 结束 ===');
      } catch (error) {
        console.error('❌ 预算图表初始化失败:', error);
        console.error('错误详情:', { message: error.message, stack: error.stack });
      }
    },
    initCorr() {
      try {
        console.log('=== initCorr 开始 ===');
        console.log('1. 获取图表容器引用');
        let el = this.$refs[this.refs.corr];
        console.log('2. 原始容器引用:', el);
        
        // 处理refs可能返回数组的情况
        if (Array.isArray(el)) {
          console.log('3. 容器是数组，取第一个元素');
          el = el[0];
        }
        
        console.log('4. 处理后的容器:', el);
        
        if (!el) {
          console.warn('5. 增长率图表容器未找到，跳过初始化');
          return;
        }
        
        // 检查元素是否为有效的DOM元素
        if (!el.tagName || typeof el.getBoundingClientRect !== 'function') {
          console.warn('6. 增长率图表容器不是有效的DOM元素:', el);
          return;
        }
        
        console.log('7. 增长率图表容器类型:', el.tagName, '类名:', el.className);
        console.log('8. 容器尺寸:', el.getBoundingClientRect());
        
        // 如果已存在图表，先销毁
        if (this.charts.corr) {
          console.log('9. 销毁现有图表');
          this.charts.corr.dispose();
        }
        
        console.log('10. 初始化ECharts实例');
        const chart = echarts.init(el);
        this.charts.corr = chart;
        console.log('11. ECharts实例创建成功:', chart);
        
        // 使用真实的增长率数据
        console.log('12. 获取增长率数据');
        const { monthlyData, indicators, growthLimits } = this.growth;
        console.log('13. 增长率数据:', { monthlyData, indicators, growthLimits });
        
        if (!monthlyData || monthlyData.length === 0) {
          console.warn('14. 增长率数据为空，显示暂无数据');
          
          chart.setOption({
            title: {
              text: '暂无增长率数据',
              left: 'center',
              top: 'middle',
              textStyle: { color: '#999', fontSize: 14 }
            }
          });
        } else {
          console.log('15. 使用真实增长率数据');
          // 使用真实数据
          const months = monthlyData.map(item => item.month);
          const isCurrentMonth = monthlyData.map(item => item.isCurrentMonth);
          // 计算当月增长率(%)：优先使用后端提供字段，其次用相邻月金额推导
          const growthPercents = monthlyData.map((item, idx, arr) => {
            const hasMom = item?.currentMomGrowth != null ? item.currentMomGrowth : (item?.momGrowth != null ? item.momGrowth : (item?.growth != null ? item.growth : null));
            if (hasMom != null && isFinite(Number(hasMom))) return Number(hasMom) * 100;
            const cur = Number(item?.value);
            const prev = idx > 0 ? Number(arr[idx - 1]?.value) : NaN;
            if (isFinite(cur) && isFinite(prev) && prev !== 0) return ((cur - prev) / prev) * 100;
            return null;
          });
          
          // 控制限(%)：假定后端返回为小数，统一转为百分比；若后端未返回则兜底为0，避免整条线缺失
          const toPct = (v) => (typeof v === 'number' && isFinite(v) ? v * 100 : 0);
          const meanPct = toPct(growthLimits?.historicalMean);
          const up2Pct = toPct(growthLimits?.upper2Sigma);
          const lo2Pct = toPct(growthLimits?.lower2Sigma);
          const up3Pct = toPct(growthLimits?.upper3Sigma);
          const lo3Pct = toPct(growthLimits?.lower3Sigma);

          console.log('16. 处理后的数据:', { months, growthPercents, isCurrentMonth, meanPct, up2Pct, lo2Pct, up3Pct, lo3Pct });
          
          chart.setOption({
            title: {
              text: '费用增长率分析',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { 
              trigger: 'axis',
              formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                  const val = param.value;
                  const text = (val == null || !isFinite(val)) ? '-' : `${Number(val).toFixed(1)}%`;
                  const isCur = isCurrentMonth[params[0].dataIndex];
                  const marker = isCur && param.seriesName === '当月增长率' ? '🔴' : param.marker;
                  result += `${marker}${param.seriesName}: ${text}${isCur && param.seriesName === '当月增长率' ? ' (当前月)' : ''}<br/>`;
                });
                return result;
              }
            },
            legend: { 
              top: 35,
              data: ['当月增长率', '历史均值', '+2σ', '-2σ', '+3σ', '-3σ']
            },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { 
              type: 'category', 
              data: months,
              axisLabel: { rotate: 45 }
            },
            yAxis: { 
              type: 'value', 
              axisLabel: { 
                formatter: val => `${Number(val).toFixed(1)}%`,
                fontSize: 12
              },
              name: '增长率(%)'
            },
            series: [
              {
                name: '当月增长率',
                type: 'line',
                data: growthPercents,
                connectNulls: true,
                symbolSize: function(value, params) {
                  return isCurrentMonth[params.dataIndex] ? 10 : 6;
                },
                itemStyle: function(params) {
                  return {
                    color: isCurrentMonth[params.dataIndex] ? '#f56c6c' : '#409eff'
                  };
                },
                lineStyle: { width: 2 }
              },
              {
                name: '历史均值',
                type: 'line',
                data: months.map(() => meanPct),
                lineStyle: { type: 'dashed', color: '#67c23a', width: 2 }
              },
              {
                name: '+2σ',
                type: 'line',
                data: months.map(() => up2Pct),
                lineStyle: { type: 'dashed', color: '#e6a23c' }
              },
              {
                name: '-2σ',
                type: 'line',
                data: months.map(() => lo2Pct),
                lineStyle: { type: 'dashed', color: '#e6a23c' }
              },
              {
                name: '+3σ',
                type: 'line',
                data: months.map(() => up3Pct),
                lineStyle: { type: 'dotted', color: '#f56c6c' }
              },
              {
                name: '-3σ',
                type: 'line',
                data: months.map(() => lo3Pct),
                lineStyle: { type: 'dotted', color: '#f56c6c' }
              }
            ]
          });
        }
        
        console.log('17. ✅ 增长率图表配置设置成功');
        
        // 验证图表是否渲染
        setTimeout(() => {
          const canvas = el.querySelector('canvas');
          console.log('18. 检查canvas元素:', canvas);
          if (canvas) {
            console.log('19. ✅ Canvas元素存在，增长率图表应该已渲染');
          } else {
            console.warn('20. ⚠️ Canvas元素未找到，增长率图表可能未渲染');
          }
        }, 100);
        
        console.log('=== initCorr 结束 ===');
      } catch (error) {
        console.error('❌ 增长率图表初始化失败:', error);
        console.error('错误详情:', { message: error.message, stack: error.stack });
      }
    },
    initForecast() {
      try {
        console.log('=== initForecast 开始 ===');
        console.log('1. 获取图表容器引用');
        const chartDom = document.getElementById('forecastTrendChart');
        console.log('2. 预测图表容器:', chartDom);
        
        if (!chartDom) {
          console.warn('3. 预测图表容器未找到，跳过初始化');
          return;
        }
        
        // 检查元素是否为有效的DOM元素
        if (!chartDom.tagName || typeof chartDom.getBoundingClientRect !== 'function') {
          console.warn('4. 预测图表容器不是有效的DOM元素:', chartDom);
          return;
        }
        
        console.log('5. 预测图表容器类型:', chartDom.tagName, '类名:', chartDom.className);
        console.log('6. 容器尺寸:', chartDom.getBoundingClientRect());
        
        // 如果已存在图表，先销毁
        if (this.forecastTrendChartInstance) {
          console.log('7. 销毁现有预测图表');
          this.forecastTrendChartInstance.dispose();
        }
        
        console.log('8. 初始化ECharts实例');
        const chart = echarts.init(chartDom);
        this.forecastTrendChartInstance = chart;
        console.log('9. ECharts实例创建成功:', chart);
        
        // 使用真实的预测数据
        console.log('10. 获取预测数据');
        const { monthlyData, indicators, forecastData } = this.forecast;
        console.log('11. 预测数据:', { monthlyData, indicators, forecastData });
        
        if (!monthlyData || monthlyData.length === 0) {
          console.warn('12. 预测数据为空，使用默认数据');
          
          // 生成默认的预测数据
          const months = this.months;
          const historicalData = this.seriesMonthly;
          
          // 简单的线性回归预测
          const x = historicalData.map((_, i) => i);
          const y = historicalData;
          const n = x.length;
          const sumX = x.reduce((a, b) => a + b, 0);
          const sumY = y.reduce((a, b) => a + b, 0);
          const sumXY = x.reduce((s, xi, i) => s + xi * y[i], 0);
          const sumXX = x.reduce((s, xi) => s + xi * xi, 0);
          const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
          const intercept = (sumY - slope * sumX) / n;
          
          // 生成预测数据（未来6个月）
          const futureMonths = [];
          const predictedData = [];
          const budgetData = [];
          
          for (let i = 0; i < 12; i++) {
            const monthIndex = i + 1;
            const monthName = `${monthIndex}月`;
            futureMonths.push(monthName);
            
            if (i < historicalData.length) {
              // 历史数据
              predictedData.push(historicalData[i]);
              budgetData.push(historicalData[i] * 1.1); // 预算比实际高10%
            } else {
              // 预测数据
              const predictedValue = Math.round(slope * i + intercept);
              predictedData.push(predictedValue);
              budgetData.push(predictedValue * 1.1);
            }
          }
          
          console.log('13. 默认预测数据:', { futureMonths, predictedData, budgetData });
          
          chart.setOption({
            title: {
              text: '费用趋势预测分析（默认数据）',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { 
              trigger: 'axis',
              formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                  const value = param.value.toLocaleString();
                  result += param.marker + param.seriesName + ': ¥' + value + '<br/>';
                });
                return result;
              }
            },
            legend: { 
              top: 35,
              data: ['预算值', '实际累计', '预测累计']
            },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { 
              type: 'category', 
              data: futureMonths,
              axisLabel: { rotate: 45 }
            },
            yAxis: { 
              type: 'value', 
              axisLabel: { formatter: val => `¥${(val/1000)}k` },
              name: '费用金额(千元)'
            },
            series: [
              { 
                name: '预算值', 
                type: 'line', 
                data: budgetData,
                lineStyle: { type: 'dashed', color: '#5B8FF9', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '实际累计', 
                type: 'line', 
                data: predictedData.slice(0, historicalData.length),
                lineStyle: { color: '#FF6B6B', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '预测累计', 
                type: 'line', 
                data: predictedData,
                lineStyle: { color: '#4ECDC4', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              }
            ]
          });
        } else {
          console.log('14. 使用真实预测数据');
          // 使用真实数据
          const { historicalData, predictedData, budgetData } = forecastData;
          
          // 合并历史数据和预测数据
          const allMonths = [];
          const allActualData = [];
          const allPredictedData = [];
          const allBudgetData = [];
          
          // 添加历史数据
          historicalData.forEach(item => {
            allMonths.push(item.month);
            allActualData.push(item.value);
            allPredictedData.push(item.value); // 历史数据中预测值等于实际值
            allBudgetData.push(item.budget || item.value * 1.1);
          });
          
          // 添加预测数据
          predictedData.forEach(item => {
            allMonths.push(item.month);
            allActualData.push(null); // 未来月份没有实际值
            allPredictedData.push(item.value);
            allBudgetData.push(item.budget || item.value * 1.1);
          });
          
          console.log('15. 处理后的数据:', { allMonths, allActualData, allPredictedData, allBudgetData });
          
          chart.setOption({
            title: {
              text: '费用趋势预测分析',
              left: 'center',
              top: 10,
              textStyle: { fontSize: 14, fontWeight: 'bold' }
            },
            tooltip: { 
              trigger: 'axis',
              formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                  if (param.value !== null) {
                    const value = param.value.toLocaleString();
                    result += param.marker + param.seriesName + ': ¥' + value + '<br/>';
                  }
                });
                return result;
              }
            },
            legend: { 
              top: 35,
              data: ['预算值', '实际累计', '预测累计']
            },
            grid: { left: 50, right: 20, top: 70, bottom: 50 },
            xAxis: { 
              type: 'category', 
              data: allMonths,
              axisLabel: { rotate: 45 }
            },
            yAxis: { 
              type: 'value', 
              axisLabel: { formatter: val => `¥${(val/1000)}k` },
              name: '费用金额(千元)'
            },
            series: [
              { 
                name: '预算值', 
                type: 'line', 
                data: allBudgetData,
                lineStyle: { type: 'dashed', color: '#5B8FF9', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '实际累计', 
                type: 'line', 
                data: allActualData,
                lineStyle: { color: '#FF6B6B', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              },
              { 
                name: '预测累计', 
                type: 'line', 
                data: allPredictedData,
                lineStyle: { color: '#4ECDC4', width: 2 },
                symbol: 'circle',
                symbolSize: 6
              }
            ]
          });
        }
        
        console.log('16. ✅ 预测图表配置设置成功');
        
        // 验证图表是否渲染
        setTimeout(() => {
          const canvas = chartDom.querySelector('canvas');
          console.log('17. 检查canvas元素:', canvas);
          if (canvas) {
            console.log('18. ✅ Canvas元素存在，预测图表应该已渲染');
          } else {
            console.warn('19. ⚠️ Canvas元素未找到，预测图表可能未渲染');
          }
        }, 100);
        
        console.log('=== initForecast 结束 ===');
      } catch (error) {
        console.error('❌ 预测图表初始化失败:', error);
        console.error('错误详情:', { message: error.message, stack: error.stack });
      }
    }
  }
}
</script>

<style scoped>
.expense-analysis { position: relative; max-height: 80vh; overflow: auto; padding-right: 6px; }
.header { position: sticky; top: 0; z-index: 20; background: #fff; padding-top: 8px; margin-bottom: 14px; border-bottom: 1px solid #ebeef5; display: flex; align-items: center; justify-content: space-between; gap: 8px; }
.header.is-fixed { position: fixed; top: 0; left: 0; right: 0; padding: 8px 12px; box-shadow: 0 2px 6px rgba(0,0,0,0.04); }
.header-left { display: block; }
.header-actions { display: flex; align-items: center; }
.param-warning { margin-top: 10px; }
.header h2 { margin: 0; font-weight: 600; }
.header .sub { color: #888; margin-top: 4px; }
.toc-float { position: absolute; top: 8px; right: 8px; z-index: 10; }
.structure-grid { display: grid; grid-template-columns: 1.2fr 1fr; gap: 12px; align-items: stretch; }
.structure-table :deep(.el-table) { font-size: 12px; }
.structure-pie { 
  width: 100%; 
  height: 320px; 
  position: relative;
  border: 1px solid #eee;
  background: #fff;
}

.trend-grid { 
  display: grid; 
  grid-template-columns: 1fr 1.5fr; 
  gap: 16px; 
  align-items: stretch; 
}
.trend-indicators { 
  min-height: 320px; 
}
.trend-chart { 
  height: 320px; 
}

.volatility-grid {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  margin-top: 20px;
}

.volatility-indicators {
  min-height: 400px;
}

.volatility-chart {
  min-height: 400px;
}

.growth-grid {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  margin-top: 20px;
}

.growth-indicators {
  min-height: 400px;
}

.growth-chart {
  min-height: 400px;
}

.forecast-grid {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  margin-top: 20px;
}

.forecast-indicators {
  min-height: 400px;
}

.forecast-chart {
  min-height: 400px;
}

.budget-grid {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  margin-top: 20px;
}

.budget-indicators {
  min-height: 400px;
}

.budget-chart {
  min-height: 400px;
}
.indicator-card { 
  height: 100%; 
}
.indicators-content { 
  padding: 8px 0; 
}
.indicator-row { 
  display: flex; 
  justify-content: space-between; 
  margin-bottom: 16px; 
}
.indicator-item { 
  flex: 1; 
  text-align: center; 
  padding: 8px; 
  background: #f8f9fa; 
  border-radius: 6px; 
  margin: 0 4px; 
}
.indicator-label { 
  font-size: 12px; 
  color: #666; 
  margin-bottom: 4px; 
}
.indicator-value { 
  font-size: 16px; 
  font-weight: 600; 
  color: #333; 
}
.indicator-value.positive { 
  color: #67c23a; 
}
.indicator-value.negative { 
  color: #f56c6c; 
}
.empty-content { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
  height: 200px; 
}

.toc-btn { padding: 6px 10px; }
.toc-title { font-weight: 600; margin-bottom: 8px; }
.toc-popover ul { list-style: none; padding-left: 0; margin: 0; max-height: 240px; overflow: auto; }
.toc-popover li { margin: 6px 0; }
.toc-popover li.active > a { color: #409eff; font-weight: 600; }
.toc-popover a { color: var(--el-text-color-regular, #606266); text-decoration: none; }
.section { margin-bottom: 20px; }
.card-header {font-size:20px;font-weight: 600; display:flex; align-items:center; justify-content:space-between; }
.section-desc { margin-bottom: 10px; }
.charts { width: 100%; }
.chart { width: 100%; height: 360px; }
.kpi-row .kpi { background: #fafafa; border: 1px solid #eee; border-radius: 6px; padding: 12px; }
.kpi-title { color:#666; font-size: 12px; }
.kpi-value { font-size: 20px; font-weight: 600; margin-top: 4px; }

/* 当前月份核心指标样式 */
.current-month-section {
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  color: #333;
  border: 1px solid #ebeef5;
}

.current-month-section .section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.current-month-section .section-title h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.current-month-kpi {
  background: #f8f9fa !important;
  border: 1px solid #e4e7ed !important;
  color: #333;
}

.current-month-kpi .kpi-title {
  color: #666;
  font-size: 12px;
}

.current-month-kpi .kpi-value {
  color: #333;
  font-size: 24px;
  font-weight: 700;
  margin-top: 8px;
}

.current-month-kpi .kpi-trend {
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.current-month-kpi .kpi-trend.up {
  color: #67c23a;
}

.current-month-kpi .kpi-trend.down {
  color: #f56c6c;
}

.current-month-kpi .kpi-subtitle {
  font-size: 11px;
  margin-top: 4px;
  color: #666;
}

/* 当前月份明细数据样式 */
.current-month-details {
  margin-bottom: 24px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

/* 当月预警信息样式，沿用明细卡片外观 */
.current-month-warnings {
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}
.warning-item-row { cursor: pointer; }
.warning-item-row:hover { background: #f9fafb; }

.details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.details-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.details-content {
  padding: 16px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}



/* 趋势样式 */
.trend-up {
  color: #67c23a;
  font-weight: 600;
}

.trend-down {
  color: #f56c6c;
  font-weight: 600;
}

.kpi-value.positive {
  color: #67c23a;
}

.kpi-value.negative {
  color: #f56c6c;
}

.kpi-value.warning {
  color: #e6a23c;
}

.kpi-subtitle.positive {
  color: #67c23a;
}

.kpi-subtitle.negative {
  color: #f56c6c;
}

.kpi-subtitle.warning {
  color: #e6a23c;
}

/* 原 original-conclusion 包裹层已移除 */

/* AI分析相关样式 */
.ai-analysis-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.ai-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.ai-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.ai-status-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-status-hint {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.ai-content {
  background: #ffffff;
  border-radius: 6px;
  padding: 20px;
  border: 1px solid #e4e7ed;
}

.analysis-item {
  margin-bottom: 15px;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-text {
  color: #666;
  line-height: 1.6;
}

/* 思考过程折叠样式 */
.think-box {
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  background: #fafafa;
  margin-bottom: 15px;
}

.think-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  color: #606266;
  font-size: 12px;
  background: #f5f7fa;
}

.think-header:hover {
  background: #f2f6fc;
}

.think-content {
  padding: 8px 12px 12px 12px;
  font-size: 12px;
  color: #666;
  background: #fafafa;
}

.ai-answer {
  white-space: pre-wrap;
  word-break: break-word;
  margin-top: 8px;
  line-height: 1.6;
}

.ai-answer :deep(.ai-sec-title) {
  font-weight: 700;
  font-size: 18px;
  display: inline-block;
  margin-top: 8px;
}

/* 强制重置 markdown 元素的上下外边距为 0（在 AI 内容容器内生效） */
.ai-content :deep(h1),
.ai-content :deep(h2),
.ai-content :deep(h3),
.ai-content :deep(h4),
.ai-content :deep(h5),
.ai-content :deep(h6),
.ai-content :deep(p),
.ai-content :deep(ul),
.ai-content :deep(ol),
.ai-content :deep(li),
.ai-content :deep(pre),
.ai-content :deep(code),
.ai-content :deep(blockquote),
.ai-content :deep(table),
.ai-content :deep(thead),
.ai-content :deep(tbody),
.ai-content :deep(tr),
.ai-content :deep(th),
.ai-content :deep(td) {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.ai-actions {
  margin-top: 15px;
  text-align: center;
}

/* AI解读结论样式 */
.ai-conclusion-content {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  line-height: 1.6;
  color: #333;
}

.ai-conclusion-content :deep(h1),
.ai-conclusion-content :deep(h2),
.ai-conclusion-content :deep(h3),
.ai-conclusion-content :deep(h4),
.ai-conclusion-content :deep(h5),
.ai-conclusion-content :deep(h6) {
  margin: 12px 0 8px 0;
  font-weight: 600;
  color: #303133;
}

.ai-conclusion-content :deep(h1) { font-size: 18px; }
.ai-conclusion-content :deep(h2) { font-size: 16px; }
.ai-conclusion-content :deep(h3) { font-size: 15px; }
.ai-conclusion-content :deep(h4) { font-size: 14px; }
.ai-conclusion-content :deep(h5) { font-size: 13px; }
.ai-conclusion-content :deep(h6) { font-size: 12px; }

.ai-conclusion-content :deep(p) {
  margin: 8px 0;
  line-height: 1.6;
}

.ai-conclusion-content :deep(ul),
.ai-conclusion-content :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.ai-conclusion-content :deep(li) {
  margin: 4px 0;
  line-height: 1.5;
}

.ai-conclusion-content :deep(strong),
.ai-conclusion-content :deep(b) {
  font-weight: 600;
  color: #303133;
}

.ai-conclusion-content :deep(em),
.ai-conclusion-content :deep(i) {
  font-style: italic;
  color: #606266;
}

.ai-conclusion-content :deep(code) {
  background: #f1f2f3;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.ai-conclusion-content :deep(pre) {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  overflow-x: auto;
  margin: 8px 0;
}

.ai-conclusion-content :deep(blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 12px;
  margin: 8px 0;
  color: #606266;
  font-style: italic;
}

.ai-conclusion-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 8px 0;
}

.ai-conclusion-content :deep(th),
.ai-conclusion-content :deep(td) {
  border: 1px solid #e4e7ed;
  padding: 8px 12px;
  text-align: left;
}

.ai-conclusion-content :deep(th) {
  background: #f5f7fa;
  font-weight: 600;
}

.no-ai-content {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  text-align: center;
}

.conclusion ul { padding-left: 18px; }
.table-footer { 
  padding: 8px 12px; 
  background: #f8f9fa; 
  border-top: 1px solid #ebeef5; 
  text-align: right; 
  font-size: 12px; 
}
.table-footer .total-label { color: #666; margin-right: 8px; }
.table-footer .total-amount { font-weight: 600; color: #409eff; margin-right: 4px; }
.table-footer .total-ratio { color: #999; }
.text-muted { color: #999; }
</style>

<!-- 打印导出样式：必须为全局样式，避免 scoped 屏蔽 -->
<style>
@media print {
  /* 隐藏导航、按钮等，仅保留正文 */
  .toc-float, .el-popover, .export-pdf-btn, .toc-btn, .el-button--mini { display: none !important; }
  /* 展开容器，移除滚动限制 */
  .expense-analysis { max-height: none !important; overflow: visible !important; }
  /* 章节分页，保证各章节分别起始新页 */
  .expense-analysis .section { page-break-before: always; }
  .expense-analysis .section:first-of-type { page-break-before: auto; }
  /* 保留色彩 */
  html, body { -webkit-print-color-adjust: exact; print-color-adjust: exact; background: #ffffff; }
}
</style>

