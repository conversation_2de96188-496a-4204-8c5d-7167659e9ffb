package org.springblade.common.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.reflect.Array;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.Field;
import java.util.ArrayList;

/**
 * Word (DOC/DOCX) 转 PDF 工具（Aspose.Words 24.12）
 *
 * 说明：为避免编译期强依赖，本工具通过反射调用 Aspose.Words。
 * - 运行前请确保引入 Aspose.Words 24.12 对应 Jar（或已在容器/本地 Maven 可获取）。
 * - 可选设置 License 以去除评估水印。
 */
public class WordPdfUtil {

    /**
     * 可选：设置 Aspose.Words 许可证，避免评估水印
     */
    public static void setLicense(InputStream licenseStream) {
        if (licenseStream == null)
            return;
        try {
            Class<?> licenseClz = Class.forName("com.aspose.words.License");
            Object license = licenseClz.getDeclaredConstructor().newInstance();
            Method setLicense = licenseClz.getMethod("setLicense", InputStream.class);
            setLicense.invoke(license, licenseStream);
        } catch (Throwable e) {
            System.err.println("设置 Aspose.Words 许可证失败: " + e.getMessage());
        }
    }

    /**
     * 将 Word 文档转换为 PDF
     * 
     * @param inputDoc  输入的 DOC/DOCX 文件
     * @param outputPdf 输出的 PDF 文件
     */
    public static void convertToPdf(File inputDoc, File outputPdf) {
        if (inputDoc == null || !inputDoc.exists()) {
            throw new IllegalArgumentException("输入文件不存在: " + (inputDoc == null ? "null" : inputDoc.getAbsolutePath()));
        }
        File parent = outputPdf.getParentFile();
        if (parent != null && !parent.exists()) {
            if (!parent.mkdirs()) {
                throw new RuntimeException("创建输出目录失败: " + parent.getAbsolutePath());
            }
        }

        try {
            // ====== 新增：设置 FontSettings 加载自定义字体目录 ======
            Class<?> fontSettingsClz = Class.forName("com.aspose.words.FontSettings");
            Object fontSettings = fontSettingsClz.newInstance();

            Class<?> fontSourceBaseClz = Class.forName("com.aspose.words.FontSourceBase");
            Class<?> folderFontSourceClz = Class.forName("com.aspose.words.FolderFontSource");
            Constructor<?> folderCtor = folderFontSourceClz.getConstructor(String.class, boolean.class);
            // 指定字体目录
            Object folderFontSource = folderCtor.newInstance("/blade/fonts/windows", true);

            // 创建 FontSourceBase[] 数组
            Object fontSourcesArray = Array.newInstance(fontSourceBaseClz, 1);
            Array.set(fontSourcesArray, 0, folderFontSource);

            // 设置字体源
            Method setFontsSources = fontSettingsClz.getMethod("setFontsSources", fontSourceBaseClz.arrayType());
            setFontsSources.invoke(fontSettings, fontSourcesArray);

            // ====== 新增：创建 LoadOptions 并设置 FontSettings ======
            Class<?> loadOptionsClz = Class.forName("com.aspose.words.LoadOptions");
            Constructor<?> loadOptionsCtor = loadOptionsClz.getConstructor();
            Object loadOptions = loadOptionsCtor.newInstance();

            Method setFontSettings = loadOptionsClz.getMethod("setFontSettings", fontSettingsClz);
            setFontSettings.invoke(loadOptions, fontSettings);

            // ====== 原有逻辑：加载文档（使用 LoadOptions）======
            Class<?> docClz = Class.forName("com.aspose.words.Document");
            // 使用带 LoadOptions 的构造函数
            Constructor<?> ctor = docClz.getConstructor(String.class, loadOptionsClz);
            Object doc = ctor.newInstance(inputDoc.getAbsolutePath(), loadOptions);

            // SaveFormat.PDF
            Class<?> saveFormatClz = Class.forName("com.aspose.words.SaveFormat");
            int pdfEnum = (int) saveFormatClz.getField("PDF").get(null);

            // doc.save(String, int)
            Method save = docClz.getMethod("save", String.class, int.class);
            save.invoke(doc, outputPdf.getAbsolutePath(), pdfEnum);

            System.out.println("转换成功: " + outputPdf.getAbsolutePath());
        } catch (Throwable e) {
            throw new RuntimeException("Word 转 PDF 失败: " + e.getMessage(), e);
        }
    }

    /**
     * 本地运行测试：
     * 示例：
     * java -cp target/BladeX-Boot.jar;libs/*
     * org.springblade.common.utils.WordPdfUtil in.docx out.pdf [license.lic]
     */
//    public static void main(String[] args) throws Exception {
//        registerWord2412();
//        // if (args == null || args.length < 2) {
//        // System.out.println("用法: java WordPdfUtil <输入doc/docx路径> <输出pdf路径>
//        // [licenseFile可选]");
//        // return;
//        // }
//        String inPath = "C:\\Users\\<USER>\\Downloads\\2025年1-5月财务分析 (24).docx";
//        String outPath = "C:\\Users\\<USER>\\Downloads\\2025年1-5月财务分析 (24).pdf";
//        // if (args.length >= 3) {
//        // File lic = new File(args[2]);
//        // if (lic.exists()) {
//        // try (InputStream is = new FileInputStream(lic)) {
//        // setLicense(is);
//        // }
//        // }
//        // }
//        convertToPdf(new File(inPath), new File(outPath));
//    }

    /**
     * aspose-words:jdk17:24.12 版本
     */
    public static void registerWord2412() {
        try {
            Class<?> zzodClass = Class.forName("com.aspose.words.zzod");
            Constructor<?> constructors = zzodClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            Field zzWws = zzodClass.getDeclaredField("zzWws");
            zzWws.setAccessible(true);
            zzWws.set(instance, 1);
            Field zzVZC = zzodClass.getDeclaredField("zzVZC");
            zzVZC.setAccessible(true);
            zzVZC.set(instance, 1);

            Class<?> zz83Class = Class.forName("com.aspose.words.zz83");
            constructors.setAccessible(true);
            constructors.newInstance(null, null);

            Field zzZY4 = zz83Class.getDeclaredField("zzZY4");
            zzZY4.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZY4.set(null, zzwPValue);

            Class<?> zzXuRClass = Class.forName("com.aspose.words.zzXuR");
            Field zzWE8 = zzXuRClass.getDeclaredField("zzWE8");
            zzWE8.setAccessible(true);
            zzWE8.set(null, 128);
            Field zzZKj = zzXuRClass.getDeclaredField("zzZKj");
            zzZKj.setAccessible(true);
            zzZKj.set(null, false);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
