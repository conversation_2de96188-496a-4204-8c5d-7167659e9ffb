-- 为yjzb_finance_analysis表添加新字段的迁移脚本
-- 执行时间：2025-08-29

-- 添加report_id字段
ALTER TABLE yjzb_finance_analysis
ADD COLUMN IF NOT EXISTS report_id BIGINT COMMENT '分析报告id';

-- 添加think_process字段
ALTER TABLE yjzb_finance_analysis
ADD COLUMN IF NOT EXISTS think_process TEXT COMMENT '思考过程';

-- 添加answer_content字段
ALTER TABLE yjzb_finance_analysis
ADD COLUMN IF NOT EXISTS answer_content TEXT COMMENT '回复结果';

-- 验证字段是否添加成功
SELECT column_name, data_type, is_nullable, column_comment
FROM information_schema.columns
WHERE table_name = 'yjzb_finance_analysis'
AND column_name IN ('report_id', 'think_process', 'answer_content');
