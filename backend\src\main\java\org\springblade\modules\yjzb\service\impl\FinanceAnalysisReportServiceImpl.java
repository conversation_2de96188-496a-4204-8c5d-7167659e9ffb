/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.xwpf.usermodel.*;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.yjzb.util.WordTemplateUtil;
import org.springblade.common.utils.WordPdfUtil;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.GetObjectArgs;
import org.springblade.modules.yjzb.mapper.FinanceAnalysisReportMapper;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO;
import org.springblade.modules.yjzb.service.IFinanceAnalysisReportService;
import org.springblade.modules.yjzb.service.IFinanceAnalysisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 财务分析报告列表服务实现类
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceAnalysisReportServiceImpl extends ServiceImpl<FinanceAnalysisReportMapper, FinanceAnalysisReportEntity>
        implements IFinanceAnalysisReportService {

    private final FinanceAnalysisReportMapper reportMapper;
    private final IFinanceAnalysisService financeAnalysisService;

    @Value("${oss.endpoint:http://127.0.0.1:9000}")
    private String minioEndpoint;

    @Value("${oss.access-key:}")
    private String minioAccessKey;

    @Value("${oss.secret-key:}")
    private String minioSecretKey;

    @Value("${oss.bucket-name:yjyc}")
    private String bucketName;

    @Override
    public IPage<FinanceAnalysisReportVO> selectReportPage(IPage<FinanceAnalysisReportVO> page, FinanceAnalysisReportVO report) {
        return reportMapper.selectReportPage(page, report);
    }

    @Override
    public Map<String, Object> getReportStatistics() {
        return reportMapper.getReportStatistics();
    }

    @Override
    public FinanceAnalysisReportVO getReportDetail(Long id) {
        return reportMapper.selectReportDetail(id);
    }

    @Override
    public FinanceAnalysisReportEntity createOrUpdateReport(String title, String type, Integer queryYear,
                                                            Integer compareYear, Integer startMonth, Integer endMonth) {
        // 查询是否已存在未删除的同名报告
        LambdaQueryWrapper<FinanceAnalysisReportEntity> wrapper =
                new LambdaQueryWrapper<>();
        wrapper.eq(FinanceAnalysisReportEntity::getTitle, title)
                .eq(FinanceAnalysisReportEntity::getIsDeleted, 0);
        FinanceAnalysisReportEntity existingReport = this.getOne(wrapper);

        boolean isUpdate = false;
        FinanceAnalysisReportEntity report;

        if (existingReport != null) {
            report = existingReport;
            isUpdate = true;
        } else {
            report = new FinanceAnalysisReportEntity();
            report.setTitle(title);
            report.setDownloadCount(0);
            report.setGenerateTime(new Date());
        }

        // 更新字段
        report.setType(type);
        report.setQueryYear(queryYear);
        report.setCompareYear(compareYear);
        report.setStartMonth(startMonth);
        report.setEndMonth(endMonth);
        report.setReportStatus("generating");
        report.setPeriod(String.format("%d年%d-%d月", queryYear, startMonth, endMonth));
        report.setUpdateTime(new Date());
        report.setGenerateTime(new Date()); //生成时间给当前时间

        if (isUpdate) {
            this.updateById(report);
        } else {
            this.save(report);
        }

        return report;
    }

    @Override
    public boolean updateReportStatus(Long reportId, String status, String errorMessage) {
        FinanceAnalysisReportEntity report = this.getById(reportId);
        if (report == null) {
            return false;
        }

        report.setReportStatus(status);
        if (Func.isNotBlank(errorMessage)) {
            report.setErrorMessage(errorMessage);
        }

        if ("completed".equals(status)) {
            report.setCompleteTime(new Date());
        }

        return this.updateById(report);
    }

    @Override
    public boolean completeReport(Long reportId, String filePath, String fileName, Long fileSize) {
        FinanceAnalysisReportEntity report = this.getById(reportId);
        if (report == null) {
            return false;
        }

        report.setReportStatus("completed");
        report.setFilePath(filePath);
        report.setFileName(fileName);
        report.setFileSize(fileSize);
        report.setCompleteTime(new Date());

        return this.updateById(report);
    }

    /**
     * 完成报告生成（包含PDF信息）
     */
    public boolean completeReport(Long reportId, String filePath, String fileName, Long fileSize,
                                 String pdfFilePath, String pdfFileName, Long pdfFileSize) {
        FinanceAnalysisReportEntity report = this.getById(reportId);
        if (report == null) {
            return false;
        }

        report.setReportStatus("completed");
        report.setFilePath(filePath);
        report.setFileName(fileName);
        report.setFileSize(fileSize);
        report.setPdfFilePath(pdfFilePath);
        report.setPdfFileName(pdfFileName);
        report.setPdfFileSize(pdfFileSize);
        report.setCompleteTime(new Date());

        return this.updateById(report);
    }

    @Override
    public boolean incrementDownloadCount(Long id) {
        return reportMapper.updateDownloadCount(id) > 0;
    }

    @Override
    public boolean deleteReports(String ids) {
        return this.removeByIds(Func.toLongList(ids));
    }

    @Override
    public Long generateAnalysisReport(String title, String type, Integer queryYear,
                                       Integer compareYear, Integer startMonth, Integer endMonth) {
        // 1. 创建报告记录
        FinanceAnalysisReportEntity report = createOrUpdateReport(title, type, queryYear, compareYear, startMonth, endMonth);

        // 2. 异步执行分析任务
        CompletableFuture.runAsync(() -> {
            try {
                executeAnalysisAndGenerateReport(report);
            } catch (Exception e) {
                log.error("生成分析报告失败: reportId={}", report.getId(), e);
                updateReportStatus(report.getId(), "failed", e.getMessage());
            }
        });

        return report.getId();
    }

    /**
     * 执行分析并生成报告
     */
    private void executeAnalysisAndGenerateReport(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始执行分析报告生成: reportId={}, title={}", report.getId(), report.getTitle());

            // 执行各项分析
            executeAnalysisTasks(report);

            // 生成Word文档
            generateWordDocument(report);

            log.info("分析报告生成完成: reportId={}", report.getId());

        } catch (Exception e) {
            log.error("执行分析报告生成失败: reportId={}", report.getId(), e);
            updateReportStatus(report.getId(), "failed", e.getMessage());
            throw e;
        }
    }

    /**
     * 执行分析任务
     */
    private void executeAnalysisTasks(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始执行分析任务: reportId={}, title={}", report.getId(), report.getTitle());

            // 执行四项分析任务
            List<FinanceAnalysisEntity> analysisResults = executeAllAnalysis(report);

            // 等待所有分析完成
            waitForAnalysisCompletion(analysisResults);

            log.info("分析任务执行完成: reportId={}", report.getId());

        } catch (Exception e) {
            log.error("执行分析任务失败: reportId={}", report.getId(), e);
            throw new RuntimeException("执行分析任务失败", e);
        }
    }

    /**
     * 执行所有分析
     */
    private List<FinanceAnalysisEntity> executeAllAnalysis(FinanceAnalysisReportEntity report) throws Exception {
        List<FinanceAnalysisEntity> results = new java.util.ArrayList<>();

        try {
            // 1. 实现税利情况分析+卷烟经营情况分析
            log.info("执行实现税利情况分析: reportId={}", report.getId());
            String workflowRunId1 = financeAnalysisService.analyzeMainEconomicIndicators(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId1 != null) {
                String[] workflowRunIds1 = workflowRunId1.split(",");
                for (String id : workflowRunIds1) {
                    FinanceAnalysisEntity entity1 = financeAnalysisService.getOneByWorkflowRunId(id);
                    if (entity1 != null) {
                        results.add(entity1);
                    }
                }

            }

            // 2. 三项费用支出总体情况分析
            log.info("执行三项费用支出总体情况分析: reportId={}", report.getId());
            String workflowRunId3 = financeAnalysisService.analyzeThreeExpenses(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId3 != null) {
                FinanceAnalysisEntity entity3 = financeAnalysisService.getOneByWorkflowRunId(workflowRunId3);
                if (entity3 != null) {
                    results.add(entity3);
                }
            }

            // 3. 重点费用支出情况分析
            log.info("执行重点费用支出情况分析: reportId={}", report.getId());
            String workflowRunId4 = financeAnalysisService.analyzeKeyExpenses(
                    report.getQueryYear(), report.getCompareYear(),
                    report.getStartMonth(), report.getEndMonth(), report.getId());
            if (workflowRunId4 != null) {
                FinanceAnalysisEntity entity4 = financeAnalysisService.getOneByWorkflowRunId(workflowRunId4);
                if (entity4 != null) {
                    results.add(entity4);
                }
            }

        } catch (Exception e) {
            log.error("执行分析失败: reportId={}", report.getId(), e);
            throw e;
        }

        return results;
    }

    /**
     * 等待分析完成
     */
    private void waitForAnalysisCompletion(List<FinanceAnalysisEntity> analysisResults) {
        int maxWaitTime = 300; // 最大等待5分钟
        int waitInterval = 10; // 每10秒检查一次
        int waitedTime = 0;

        while (waitedTime < maxWaitTime) {
            boolean allCompleted = true;

            for (FinanceAnalysisEntity entity : analysisResults) {
                FinanceAnalysisEntity current = financeAnalysisService.getById(entity.getId());
                if (current == null || !"COMPLETED".equals(current.getExecuteStatus())) {
                    allCompleted = false;
                    break;
                }
            }

            if (allCompleted) {
                log.info("所有分析任务已完成");
                return;
            }

            try {
                Thread.sleep(waitInterval * 1000);
                waitedTime += waitInterval;
                log.info("等待分析完成中... 已等待{}秒", waitedTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待分析完成被中断", e);
            }
        }

        log.warn("等待分析完成超时，继续生成报告");
    }

    /**
     * 生成Word文档
     */
    private void generateWordDocument(FinanceAnalysisReportEntity report) {
        try {
            log.info("开始生成Word文档: reportId={}", report.getId());

            // 获取分析结果
            List<FinanceAnalysisEntity> analysisResults = getAnalysisResults(report);

            // 生成Word文档内容
            byte[] documentBytes = createWordDocument(report, analysisResults);

            // 上传Word文档到MinIO
            String fileName = generateFileName(report);
            String filePath = uploadToMinio(documentBytes, fileName);

            // 生成PDF文档并上传
            byte[] pdfBytes = convertWordToPdf(documentBytes);
            String pdfFileName = generatePdfFileName(report);
            String pdfFilePath = uploadPdfToMinio(pdfBytes, pdfFileName);

            // 更新报告状态（包含PDF信息）
            completeReport(report.getId(), filePath, fileName, (long) documentBytes.length,
                          pdfFilePath, pdfFileName, (long) pdfBytes.length);

            log.info("Word文档生成完成: reportId={}, fileName={}, size={}",
                    report.getId(), fileName, documentBytes.length);

        } catch (Exception e) {
            log.error("生成Word文档失败: reportId={}", report.getId(), e);
            throw new RuntimeException("生成Word文档失败", e);
        }
    }

    /**
     * 获取分析结果
     */
    private List<FinanceAnalysisEntity> getAnalysisResults(FinanceAnalysisReportEntity report) {
        // 根据报告的查询条件获取对应的分析结果
        return financeAnalysisService.list(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<FinanceAnalysisEntity>()
                        .eq(FinanceAnalysisEntity::getQueryYear, report.getQueryYear())
                        .eq(FinanceAnalysisEntity::getCompareYear, report.getCompareYear())
                        .eq(FinanceAnalysisEntity::getStartMonth, report.getStartMonth())
                        .eq(FinanceAnalysisEntity::getEndMonth, report.getEndMonth())
                        .eq(FinanceAnalysisEntity::getExecuteStatus, "COMPLETED")
                        .orderByDesc(FinanceAnalysisEntity::getExecuteTime)
        );
    }

    /**
     * 创建Word文档
     */
    private byte[] createWordDocument(FinanceAnalysisReportEntity report, List<FinanceAnalysisEntity> analysisResults)
            throws IOException {
        try (XWPFDocument document = loadTemplate();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 替换模板中的内容
            replaceTemplateContent(document, analysisResults, report);

            document.write(out);
            return out.toByteArray();
        }
    }



    /**
     * 加载Word模板文件
     */
    private XWPFDocument loadTemplate() throws IOException {
        String templatePath = "templates/财务分析下载模板.docx";
        try {
            return WordTemplateUtil.loadTemplate(templatePath);
        } catch (IOException e) {
            log.error("加载财务分析模板失败: {}", templatePath, e);
            throw new IOException("模板文件不存在或加载失败: " + templatePath, e);
        }
    }

    /**
     * 替换模板中的内容
     */
    private void replaceTemplateContent(XWPFDocument document, List<FinanceAnalysisEntity> analysisResults, FinanceAnalysisReportEntity report) {
        // 准备基本占位符数据
        Map<String, String> basicPlaceholders = new HashMap<>();
        String queryDate =  report.getQueryYear() + "年" + report.getStartMonth() + "-" + report.getEndMonth() + "月";
        String compareDate =  report.getCompareYear() + "年" + report.getStartMonth() + "-" + report.getEndMonth() + "月";
        String queryYearBudget =  report.getQueryYear() + "年预算数";

        basicPlaceholders.put("查询日期", queryDate);
        basicPlaceholders.put("查询日期最后一天", report.getQueryYear() + "年" + report.getEndMonth() + "月" + getLastDayOfMonth(report.getQueryYear(), report.getStartMonth()) + "日");
        basicPlaceholders.put("生成日期", DateUtil.format(report.getGenerateTime(), "yyyy年MM月dd日"));

        // 准备分析内容占位符数据
        Map<String, String> analysisPlaceholders = new HashMap<>();
        for (FinanceAnalysisEntity analysis : analysisResults) {
            if (analysis.getName() == null || (analysis.getAnswerContent() == null && analysis.getResult() == null)) {
                continue;
            }

            String name = analysis.getName();
            String content = analysis.getAnswerContent();
            String tableDataList = analysis.getResult();
            List<String> columns = null;

            switch (name) {
                case "主要经济指标表格":
                    // 表格数据使用工具类处理
                    columns = Arrays.asList("项目", queryDate, compareDate, "同比增减（%）", queryYearBudget, "预算执行进度（%）");
                    WordTemplateUtil.replaceTableContent(document, "${economic_indicators}", tableDataList, columns);
                    break;
                case "实现税利情况分析":
                    analysisPlaceholders.put("tax_profit_analysis", content);
                    break;
                case "卷烟经营情况分析":
                    analysisPlaceholders.put("cigarette_operation_analysis", content);
                    break;
                case "三项费用支出总体情况分析":
                    analysisPlaceholders.put("three_expenses_analysis", content);
                    break;
                case "三项费用表格":
                    // 表格数据使用工具类处理
                    columns = Arrays.asList("预算项目", "行次", queryDate, compareDate, "增减额", "增减比率%", queryYearBudget, "预算余额", "预算执行率%");
                    WordTemplateUtil.replaceTableContent(document, "${three_expenses}", tableDataList, columns);
                    break;
                case "重点费用支出情况分析":
                    analysisPlaceholders.put("key_expenses_analysis", content);
                    break;
                case "重点费用支出情况表格":
                    // 表格数据使用工具类处理
                    columns = Arrays.asList("项目", queryDate, compareDate, "同比增减（%）", queryYearBudget, "执行进度（%）");
                    WordTemplateUtil.replaceTableContent(document, "${key_expenses}", tableDataList, columns);
                    break;
                default:
                    log.warn("未知的分析类型: {}", name);
                    break;
            }
        }

        // 合并所有占位符
        Map<String, String> allPlaceholders = new HashMap<>();
        allPlaceholders.putAll(basicPlaceholders);
        allPlaceholders.putAll(analysisPlaceholders);

        // 使用工具类进行占位符替换（已支持换行符处理）
        WordTemplateUtil.replacePlaceholders(document, allPlaceholders);
    }


    /**
     * 生成文件名
     */
    private String generateFileName(FinanceAnalysisReportEntity report) {
        return String.format("%d年%d-%d月财务分析.docx",
                report.getQueryYear(), report.getStartMonth(), report.getEndMonth());
    }

    /**
     * 生成PDF文件名
     */
    private String generatePdfFileName(FinanceAnalysisReportEntity report) {
        return String.format("%d年%d-%d月财务分析.pdf",
                report.getQueryYear(), report.getStartMonth(), report.getEndMonth());
    }

    /**
     * Word转PDF - 使用统一的转换服务
     */
    private byte[] convertWordToPdf(byte[] wordBytes) {
        try {
            // 创建临时文件
            File tempWordFile = File.createTempFile("temp_word_", ".docx");
            File tempPdfFile = File.createTempFile("temp_pdf_", ".pdf");

            try {
                // 将Word字节数组写入临时文件
                try (FileOutputStream fos = new FileOutputStream(tempWordFile)) {
                    fos.write(wordBytes);
                }

                WordPdfUtil.registerWord2412();
                // 使用WordPdfUtil进行转换
                WordPdfUtil.convertToPdf(tempWordFile, tempPdfFile);

                // 读取PDF文件内容
                try (FileInputStream fis = new FileInputStream(tempPdfFile);
                     ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        baos.write(buffer, 0, bytesRead);
                    }
                    return baos.toByteArray();
                }

            } finally {
                // 清理临时文件
                if (tempWordFile.exists()) {
                    tempWordFile.delete();
                }
                if (tempPdfFile.exists()) {
                    tempPdfFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("Word转PDF转换失败", e);
            throw new RuntimeException("Word转PDF转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传到MinIO
     */
    private String uploadToMinio(byte[] documentBytes, String fileName) {
        try {
            // 直接使用MinioClient上传文件，避免依赖HTTP请求上下文
            String objectName = "finance/reports/" + System.currentTimeMillis() + "_" + fileName;

            MinioClient minioClient = getMinioClient();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(documentBytes);

            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, documentBytes.length, -1)
                            .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                            .build()
            );

            log.info("文件上传到MinIO成功: objectName={}, size={}", objectName, documentBytes.length);
            return objectName;

        } catch (Exception e) {
            log.error("上传文件到MinIO失败: fileName={}", fileName, e);
            throw new RuntimeException("上传文件失败", e);
        }
    }

    /**
     * 上传PDF到MinIO
     */
    private String uploadPdfToMinio(byte[] pdfBytes, String fileName) {
        try {
            // 直接使用MinioClient上传PDF文件
            String objectName = "finance/reports/pdf/" + System.currentTimeMillis() + "_" + fileName;

           MinioClient minioClient = getMinioClient();
           ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);

           minioClient.putObject(
                   PutObjectArgs.builder()
                           .bucket(bucketName)
                           .object(objectName)
                           .stream(inputStream, pdfBytes.length, -1)
                           .contentType("application/pdf")
                           .build()
           );

            log.info("PDF文件上传到MinIO成功: objectName={}, size={}", objectName, pdfBytes.length);
            return objectName;

        } catch (Exception e) {
            log.error("上传PDF文件到MinIO失败: fileName={}", fileName, e);
            throw new RuntimeException("上传PDF文件失败", e);
        }
    }

    /**
     * 获取MinioClient实例
     */
    private MinioClient getMinioClient() {
        try {
            return MinioClient.builder()
                    .endpoint(minioEndpoint)
                    .credentials(minioAccessKey, minioSecretKey)
                    .build();
        } catch (Exception e) {
            log.error("创建MinioClient失败", e);
            throw new RuntimeException("无法创建MinioClient", e);
        }
    }

    @Override
    public byte[] downloadReportData(Long id) {
        try {
            FinanceAnalysisReportEntity report = getById(id);
            if (report == null) {
                log.error("报告不存在，ID: {}", id);
                return null;
            }

            if (Func.isBlank(report.getFilePath())) {
                log.error("报告文件路径为空，ID: {}", id);
                return null;
            }

            if (!"completed".equals(report.getReportStatus())) {
                log.error("报告尚未生成完成，ID: {}", id);
                return null;
            }

            // 从MinIO下载文件
            return downloadFromMinio(report.getFilePath());

        } catch (Exception e) {
            log.error("下载报告数据失败: reportId={}", id, e);
            return null;
        }
    }

    /**
     * 下载PDF报告数据
     */
    public byte[] downloadPdfReportData(Long id) {
        try {
            FinanceAnalysisReportEntity report = getById(id);
            if (report == null) {
                log.error("报告不存在，ID: {}", id);
                return null;
            }

            if (Func.isBlank(report.getPdfFilePath())) {
                log.error("PDF文件路径为空，ID: {}", id);
                return null;
            }

            if (!"completed".equals(report.getReportStatus())) {
                log.error("报告尚未生成完成，ID: {}", id);
                return null;
            }

            // 从MinIO下载PDF文件
            return downloadFromMinio(report.getPdfFilePath());

        } catch (Exception e) {
            log.error("下载PDF报告数据失败: reportId={}", id, e);
            return null;
        }
    }

    /**
     * 从MinIO下载文件
     */
    private byte[] downloadFromMinio(String objectName) {
        try {
            MinioClient minioClient = getMinioClient();
            try (var inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build())) {

                return inputStream.readAllBytes();
            }
        } catch (Exception e) {
            log.error("从MinIO下载文件失败: objectName={}", objectName, e);
            throw new RuntimeException("下载文件失败", e);
        }
    }

    /**
     * 获取指定年月的最后一天
     */
    private int getLastDayOfMonth(int year, int month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            case 2:
                // 判断闰年
                if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                    return 29;
                } else {
                    return 28;
                }
            default:
                return 31;
        }
    }
}
