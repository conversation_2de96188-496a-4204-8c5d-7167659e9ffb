import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CalculatorTest {

    public static void main2(String[] args) throws JsonProcessingException {
        // 原始 JSON 字符串（来自你的 dataList）
        String jsonData = "[{\"预算执行进度（%）\":\"52.12\",\"2025年预算数\":\"86,837.98\",\"2024年1-5月\":\"44,016.37\",\"项目\":\"税利总额\",\"2025年1-5月\":\"45,257.26\",\"同比增减（%）\":\"2.82\"},{\"预算执行进度（%）\":\"58.13\",\"2025年预算数\":\"31,006.79\",\"2024年1-5月\":\"17,128.29\",\"项目\":\"其中：利润总额\",\"2025年1-5月\":\"18,024.82\",\"同比增减（%）\":\"5.23\"},{\"预算执行进度（%）\":\"48.78\",\"2025年预算数\":\"55,831.19\",\"2024年1-5月\":\"26,888.08\",\"项目\":\"税费（不含所得税）\",\"2025年1-5月\":\"27,232.44\",\"同比增减（%）\":\"1.28\"},{\"预算执行进度（%）\":\"44.20\",\"2025年预算数\":\"89,250.00\",\"2024年1-5月\":\"39,224.00\",\"项目\":\"卷烟销售数量\",\"2025年1-5月\":\"39,448.00\",\"同比增减（%）\":\"0.57\"},{\"预算执行进度（%）\":\"46.75\",\"2025年预算数\":\"320,576.36\",\"2024年1-5月\":\"146,603.69\",\"项目\":\"卷烟销售收入\",\"2025年1-5月\":\"149,864.97\",\"同比增减（%）\":\"2.22\"},{\"预算执行进度（%）\":\"46.52\",\"2025年预算数\":\"223,653.31\",\"2024年1-5月\":\"101,856.67\",\"项目\":\"卷烟销售成本\",\"2025年1-5月\":\"104,054.32\",\"同比增减（%）\":\"2.16\"},{\"预算执行进度（%）\":\"47.26\",\"2025年预算数\":\"96,923.05\",\"2024年1-5月\":\"44,747.02\",\"项目\":\"毛利额\",\"2025年1-5月\":\"45,810.65\",\"同比增减（%）\":\"2.38\"},{\"预算执行进度（%）\":\"101.12\",\"2025年预算数\":\"30.23\",\"2024年1-5月\":\"30.52\",\"项目\":\"毛利率（%）\",\"2025年1-5月\":\"30.57\",\"同比增减（%）\":\"0.16\"},{\"预算执行进度（%）\":\"41.94\",\"2025年预算数\":\"70.00\",\"2024年1-5月\":\"42.72\",\"项目\":\"其他业务收入\",\"2025年1-5月\":\"29.36\",\"同比增减（%）\":\"-31.27\"},{\"预算执行进度（%）\":\"41.60\",\"2025年预算数\":\"196.00\",\"2024年1-5月\":\"75.95\",\"项目\":\"其他业务成本\",\"2025年1-5月\":\"81.54\",\"同比增减（%）\":\"7.36\"},{\"预算执行进度（%）\":\"33.13\",\"2025年预算数\":\"22,082.26\",\"2024年1-5月\":\"7,398.15\",\"项目\":\"三项费用总额\",\"2025年1-5月\":\"7,315.04\",\"同比增减（%）\":\"-1.12\"},{\"预算执行进度（%）\":\"70.83\",\"2025年预算数\":\"6.89\",\"2024年1-5月\":\"5.05\",\"项目\":\"三项费用率（%）\",\"2025年1-5月\":\"4.88\",\"同比增减（%）\":\"-3.37\"}]";

        // 使用 Jackson 解析 JSON
        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, Object>> indicatorsData = mapper.readValue(jsonData, new TypeReference<List<Map<String, Object>>>() {});

        // 设置参数（根据你的需求调整）
        Integer queryYear = 2025;
        Integer compareYear = 2024;
        Integer startMonth = 1;
        Integer endMonth = 5;

        CalculatorTest calculator = new CalculatorTest();

        // 调用方法
        Map<String, Object> result = calculator.calculateCigaretteOperationIndicatorValues(
                indicatorsData, queryYear, compareYear, startMonth, endMonth);

        // 输出结果
        System.out.println("计算结果：");
        result.forEach((key, value) -> System.out.println(key + " = " + value));
    }

    public static void main3(String[] args) throws JsonProcessingException {
        // 原始 JSON 字符串（来自你的 dataList）
        String jsonData = "[{\"2025年预算数\":\"21,243.00\",\"行次\":2,\"2024年1-5月\":\"7,398.16\",\"预算执行率%\":\"34.44\",\"增减比率%\":\"-1.12\",\"预算余额\":\"13,927.96\",\"2025年1-5月\":\"7,315.04\",\"增减额\":\"-83.12\",\"预算项目\":\"合计\"},{\"2025年预算数\":\"256.30\",\"行次\":3,\"2024年1-5月\":\"70.12\",\"预算执行率%\":\"57.20\",\"增减比率%\":\"109.05\",\"预算余额\":\"109.71\",\"2025年1-5月\":\"146.59\",\"增减额\":\"76.47\",\"预算项目\":\"修理费\"},{\"2025年预算数\":\"-539.00\",\"行次\":4,\"2024年1-5月\":\"-307.57\",\"预算执行率%\":\"46.18\",\"增减比率%\":\"-19.08\",\"预算余额\":\"-290.12\",\"2025年1-5月\":\"-248.88\",\"增减额\":\"58.69\",\"预算项目\":\"财务费用\"},{\"2025年预算数\":\"100.00\",\"行次\":5,\"2024年1-5月\":\"30.33\",\"预算执行率%\":\"69.62\",\"增减比率%\":\"129.57\",\"预算余额\":\"30.38\",\"2025年1-5月\":\"69.62\",\"增减额\":\"39.30\",\"预算项目\":\"专卖打假经费\"},{\"2025年预算数\":\"10.00\",\"行次\":6,\"2024年1-5月\":\"0.18\",\"预算执行率%\":\"99.69\",\"增减比率%\":\"5389.70\",\"预算余额\":\"0.03\",\"2025年1-5月\":\"9.97\",\"增减额\":\"9.79\",\"预算项目\":\"专卖打私经费\"},{\"2025年预算数\":\"60.00\",\"行次\":7,\"2024年1-5月\":\"13.39\",\"预算执行率%\":\"32.84\",\"增减比率%\":\"47.21\",\"预算余额\":\"40.29\",\"2025年1-5月\":\"19.71\",\"增减额\":\"6.32\",\"预算项目\":\"企业文化建设费\"},{\"2025年预算数\":\"94.00\",\"行次\":8,\"2024年1-5月\":\"29.22\",\"预算执行率%\":\"37.68\",\"增减比率%\":\"21.22\",\"预算余额\":\"58.58\",\"2025年1-5月\":\"35.42\",\"增减额\":\"6.20\",\"预算项目\":\"中介费\"},{\"2025年预算数\":\"154.00\",\"行次\":9,\"2024年1-5月\":\"103.72\",\"预算执行率%\":\"71.01\",\"增减比率%\":\"5.44\",\"预算余额\":\"44.64\",\"2025年1-5月\":\"109.36\",\"增减额\":\"5.64\",\"预算项目\":\"无形资产摊销\"},{\"2025年预算数\":\"237.00\",\"行次\":10,\"2024年1-5月\":\"327.62\",\"预算执行率%\":\"140.22\",\"增减比率%\":\"1.43\",\"预算余额\":\"-95.31\",\"2025年1-5月\":\"332.31\",\"增减额\":\"4.69\",\"预算项目\":\"劳务费\"},{\"2025年预算数\":\"16.00\",\"行次\":11,\"2024年1-5月\":\"1.10\",\"预算执行率%\":\"32.41\",\"增减比率%\":\"371.42\",\"预算余额\":\"10.81\",\"2025年1-5月\":\"5.19\",\"增减额\":\"4.09\",\"预算项目\":\"绿化费\"},{\"2025年预算数\":\"8.00\",\"行次\":12,\"2024年1-5月\":\"-0.06\",\"预算执行率%\":\"30.22\",\"增减比率%\":\"-4110.23\",\"预算余额\":\"5.58\",\"2025年1-5月\":\"2.42\",\"增减额\":\"2.48\",\"预算项目\":\"业务招待费\"},{\"2025年预算数\":\"6.50\",\"行次\":13,\"2024年1-5月\":\"4.36\",\"预算执行率%\":\"72.29\",\"增减比率%\":\"7.73\",\"预算余额\":\"1.80\",\"2025年1-5月\":\"4.70\",\"增减额\":\"0.34\",\"预算项目\":\"会议费\"},{\"2025年预算数\":\"20.00\",\"行次\":14,\"2024年1-5月\":\"0.40\",\"预算执行率%\":\"3.25\",\"增减比率%\":\"62.50\",\"预算余额\":\"19.35\",\"2025年1-5月\":\"0.65\",\"增减额\":\"0.25\",\"预算项目\":\"协会会费\"},{\"2025年预算数\":\"396.00\",\"行次\":15,\"2024年1-5月\":\"162.51\",\"预算执行率%\":\"41.04\",\"增减比率%\":\"0.00\",\"预算余额\":\"233.49\",\"2025年1-5月\":\"162.51\",\"增减额\":\"0.00\",\"预算项目\":\"租赁费\"},{\"2025年预算数\":\"24.00\",\"行次\":17,\"2024年1-5月\":\"0.00\",\"预算执行率%\":\"0.00\",\"增减比率%\":\"\",\"预算余额\":\"24.00\",\"2025年1-5月\":\"0.00\",\"增减额\":\"0.00\",\"预算项目\":\"政府性基金\"},{\"2025年预算数\":\"4.50\",\"行次\":18,\"2024年1-5月\":\"0.00\",\"预算执行率%\":\"0.00\",\"增减比率%\":\"\",\"预算余额\":\"4.50\",\"2025年1-5月\":\"0.00\",\"增减额\":\"0.00\",\"预算项目\":\"文明吸烟环境建设费\"},{\"2025年预算数\":\"71.00\",\"行次\":19,\"2024年1-5月\":\"0.00\",\"预算执行率%\":\"0.00\",\"增减比率%\":\"\",\"预算余额\":\"71.00\",\"2025年1-5月\":\"0.00\",\"增减额\":\"0.00\",\"预算项目\":\"交易手续费\"},{\"2025年预算数\":\"400.00\",\"行次\":20,\"2024年1-5月\":\"0.00\",\"预算执行率%\":\"0.00\",\"增减比率%\":\"\",\"预算余额\":\"400.00\",\"2025年1-5月\":\"0.00\",\"增减额\":\"0.00\",\"预算项目\":\"零售终端建设费\"},{\"2025年预算数\":\"11.00\",\"行次\":16,\"2024年1-5月\":\"3.16\",\"预算执行率%\":\"28.75\",\"增减比率%\":\"0.00\",\"预算余额\":\"7.84\",\"2025年1-5月\":\"3.16\",\"增减额\":\"0.00\",\"预算项目\":\"长期待摊费用摊销\"},{\"2025年预算数\":\"55.00\",\"行次\":21,\"2024年1-5月\":\"0.13\",\"预算执行率%\":\"0.00\",\"增减比率%\":\"-100.00\",\"预算余额\":\"55.00\",\"2025年1-5月\":\"0.00\",\"增减额\":\"-0.13\",\"预算项目\":\"劳动保护费\"},{\"2025年预算数\":\"57.00\",\"行次\":22,\"2024年1-5月\":\"0.61\",\"预算执行率%\":\"0.78\",\"增减比率%\":\"-26.75\",\"预算余额\":\"56.55\",\"2025年1-5月\":\"0.45\",\"增减额\":\"-0.16\",\"预算项目\":\"书报费\"},{\"2025年预算数\":\"76.00\",\"行次\":23,\"2024年1-5月\":\"21.41\",\"预算执行率%\":\"26.76\",\"增减比率%\":\"-5.02\",\"预算余额\":\"55.66\",\"2025年1-5月\":\"20.34\",\"增减额\":\"-1.08\",\"预算项目\":\"通讯费\"},{\"2025年预算数\":\"23.00\",\"行次\":24,\"2024年1-5月\":\"8.03\",\"预算执行率%\":\"29.09\",\"增减比率%\":\"-16.64\",\"预算余额\":\"16.31\",\"2025年1-5月\":\"6.69\",\"增减额\":\"-1.34\",\"预算项目\":\"车杂费\"},{\"2025年预算数\":\"23.00\",\"行次\":25,\"2024年1-5月\":\"7.68\",\"预算执行率%\":\"26.13\",\"增减比率%\":\"-21.70\",\"预算余额\":\"16.99\",\"2025年1-5月\":\"6.01\",\"增减额\":\"-1.67\",\"预算项目\":\"物业管理费\"},{\"2025年预算数\":\"42.00\",\"行次\":26,\"2024年1-5月\":\"4.52\",\"预算执行率%\":\"6.54\",\"增减比率%\":\"-39.26\",\"预算余额\":\"39.25\",\"2025年1-5月\":\"2.75\",\"增减额\":\"-1.78\",\"预算项目\":\"保险费\"},{\"2025年预算数\":\"95.00\",\"行次\":27,\"2024年1-5月\":\"2.79\",\"预算执行率%\":\"0.00\",\"增减比率%\":\"-100.00\",\"预算余额\":\"95.00\",\"2025年1-5月\":\"0.00\",\"增减额\":\"-2.79\",\"预算项目\":\"信息系统维护费\"},{\"2025年预算数\":\"120.00\",\"行次\":28,\"2024年1-5月\":\"40.29\",\"预算执行率%\":\"30.14\",\"增减比率%\":\"-10.23\",\"预算余额\":\"83.83\",\"2025年1-5月\":\"36.17\",\"增减额\":\"-4.12\",\"预算项目\":\"水电费\"},{\"2025年预算数\":\"15.00\",\"行次\":29,\"2024年1-5月\":\"6.43\",\"预算执行率%\":\"10.27\",\"增减比率%\":\"-76.02\",\"预算余额\":\"13.46\",\"2025年1-5月\":\"1.54\",\"增减额\":\"-4.89\",\"预算项目\":\"其他\"},{\"2025年预算数\":\"75.00\",\"行次\":30,\"2024年1-5月\":\"27.58\",\"预算执行率%\":\"29.07\",\"增减比率%\":\"-20.95\",\"预算余额\":\"53.20\",\"2025年1-5月\":\"21.80\",\"增减额\":\"-5.78\",\"预算项目\":\"网络通讯费\"},{\"2025年预算数\":\"96.70\",\"行次\":31,\"2024年1-5月\":\"38.29\",\"预算执行率%\":\"33.11\",\"增减比率%\":\"-16.39\",\"预算余额\":\"64.69\",\"2025年1-5月\":\"32.01\",\"增减额\":\"-6.27\",\"预算项目\":\"燃料费\"},{\"2025年预算数\":\"72.00\",\"行次\":32,\"2024年1-5月\":\"14.76\",\"预算执行率%\":\"5.31\",\"增减比率%\":\"-74.09\",\"预算余额\":\"68.18\",\"2025年1-5月\":\"3.82\",\"增减额\":\"-10.93\",\"预算项目\":\"低值易耗品摊销\"},{\"2025年预算数\":\"60.00\",\"行次\":33,\"2024年1-5月\":\"31.18\",\"预算执行率%\":\"28.78\",\"增减比率%\":\"-44.62\",\"预算余额\":\"42.73\",\"2025年1-5月\":\"17.27\",\"增减额\":\"-13.91\",\"预算项目\":\"包装费\"},{\"2025年预算数\":\"120.00\",\"行次\":34,\"2024年1-5月\":\"51.89\",\"预算执行率%\":\"27.62\",\"增减比率%\":\"-36.12\",\"预算余额\":\"86.85\",\"2025年1-5月\":\"33.15\",\"增减额\":\"-18.74\",\"预算项目\":\"专卖管理经费\"},{\"2025年预算数\":\"140.00\",\"行次\":35,\"2024年1-5月\":\"43.60\",\"预算执行率%\":\"16.51\",\"增减比率%\":\"-47.01\",\"预算余额\":\"116.89\",\"2025年1-5月\":\"23.11\",\"增减额\":\"-20.50\",\"预算项目\":\"差旅费\"},{\"2025年预算数\":\"76.00\",\"行次\":36,\"2024年1-5月\":\"22.98\",\"预算执行率%\":\"1.05\",\"增减比率%\":\"-96.52\",\"预算余额\":\"75.20\",\"2025年1-5月\":\"0.80\",\"增减额\":\"-22.18\",\"预算项目\":\"企业研发费用\"},{\"2025年预算数\":\"35.00\",\"行次\":37,\"2024年1-5月\":\"24.63\",\"预算执行率%\":\"0.00\",\"增减比率%\":\"-100.00\",\"预算余额\":\"35.00\",\"2025年1-5月\":\"0.00\",\"增减额\":\"-24.63\",\"预算项目\":\"党组织工作经费\"},{\"2025年预算数\":\"295.00\",\"行次\":38,\"2024年1-5月\":\"113.98\",\"预算执行率%\":\"29.63\",\"增减比率%\":\"-23.32\",\"预算余额\":\"207.59\",\"2025年1-5月\":\"87.41\",\"增减额\":\"-26.57\",\"预算项目\":\"警卫消防费\"},{\"2025年预算数\":\"1,050.00\",\"行次\":39,\"2024年1-5月\":\"422.28\",\"预算执行率%\":\"36.54\",\"增减比率%\":\"-9.15\",\"预算余额\":\"666.36\",\"2025年1-5月\":\"383.64\",\"增减额\":\"-38.63\",\"预算项目\":\"折旧费\"},{\"2025年预算数\":\"194.00\",\"行次\":40,\"2024年1-5月\":\"92.25\",\"预算执行率%\":\"24.88\",\"增减比率%\":\"-47.67\",\"预算余额\":\"145.73\",\"2025年1-5月\":\"48.27\",\"增减额\":\"-43.97\",\"预算项目\":\"办公费\"},{\"2025年预算数\":\"17,194.00\",\"行次\":41,\"2024年1-5月\":\"5,984.39\",\"预算执行率%\":\"34.53\",\"增减比率%\":\"-0.79\",\"预算余额\":\"11,256.91\",\"2025年1-5月\":\"5,937.09\",\"增减额\":\"-47.30\",\"预算项目\":\"职工薪酬\"}]";

        // 使用 Jackson 解析 JSON
        ObjectMapper mapper = new ObjectMapper();
        List<Map<String, Object>> indicatorsData = mapper.readValue(jsonData, new TypeReference<List<Map<String, Object>>>() {});

        // 设置参数（根据你的需求调整）
        Integer queryYear = 2025;
        Integer compareYear = 2024;
        Integer startMonth = 1;
        Integer endMonth = 5;

        CalculatorTest calculator = new CalculatorTest();

        // 调用方法
        Map<String, Object> result = calculator.calculateThreeExpensesIndicatorValues(
                indicatorsData, queryYear, compareYear, startMonth, endMonth);

        // 输出结果
        System.out.println("计算结果：");
        result.forEach((key, value) -> System.out.println(key + " = " + value));
    }


    /**
     * 计算三项费用支出总体情况分析各项指标值
     */
    private Map<String, Object> calculateThreeExpensesIndicatorValues(List<Map<String, Object>> threeExpensesData,
                                                                      Integer queryYear, Integer compareYear,
                                                                      Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (threeExpensesData == null || threeExpensesData.isEmpty()) {
            return result;
        }

        // 从三项费用数据中提取合计行数据
        Map<String, Object> totalData = threeExpensesData.stream()
                .filter(data -> "合计".equals(data.get("预算项目")))
                .findFirst()
                .orElse(null);

        // 当前时期
        String currentPeriod =  queryYear + "年" + startMonth + "-" + endMonth + "月";
        String comparePeriod =  compareYear + "年" + startMonth + "-" + endMonth + "月";
        String queryYearBudget =  queryYear + "年预算数";
        result.put("当前时期", currentPeriod);

        if (totalData != null) {
            // 三项费用支出合计本年值
            BigDecimal currentValue = getBigDecimalValue(totalData, currentPeriod);
            BigDecimal lastYearValue = getBigDecimalValue(totalData, comparePeriod);
            BigDecimal diffAmount = getBigDecimalValue(totalData, "增减额");
            BigDecimal diffPercent = getBigDecimalValue(totalData, "增减比率%");

            result.put("三项费用支出合计本年值", currentValue != null ? currentValue.intValue() : 0);
            result.put("三项费用支出合计上年值", lastYearValue != null ? lastYearValue.intValue() : 0);

            // 三项费用同比增减额
            if (diffAmount != null) {
                result.put("三项费用同比增减额", diffAmount.intValue());
                // 判断是增加还是减少
                if (diffAmount.compareTo(BigDecimal.ZERO) >= 0) {
                    result.put("三项费用同比变化方向", "增加");
                } else {
                    result.put("三项费用同比变化方向", "减少");
                    result.put("三项费用同比增减额", diffAmount.abs().intValue()); // 取绝对值
                }
            } else {
                result.put("三项费用同比增减额", 0);
                result.put("三项费用同比变化方向", "持平");
            }

            // 三项费用同比增减幅
            if (diffPercent != null) {
                result.put("三项费用同比增减幅", diffPercent.abs().setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("三项费用同比增减幅", BigDecimal.ZERO);
            }
        }

        // 提取销售费用、管理费用、财务费用的具体数据
        Map<String, Object> salesExpenseData = findExpenseByName(threeExpensesData, "销售费用");
        Map<String, Object> managementExpenseData = findExpenseByName(threeExpensesData, "管理费用");
        Map<String, Object> financialExpenseData = findExpenseByName(threeExpensesData, "财务费用");

        if (salesExpenseData != null) {
            BigDecimal salesCurrent = getBigDecimalValue(salesExpenseData, currentPeriod);
            result.put("销售费用本年值", salesCurrent != null ? salesCurrent.intValue() : 0);
        }

        if (managementExpenseData != null) {
            BigDecimal managementCurrent = getBigDecimalValue(managementExpenseData, currentPeriod);
            result.put("管理费用本年值", managementCurrent != null ? managementCurrent.intValue() : 0);
        }

        if (financialExpenseData != null) {
            BigDecimal financialCurrent = getBigDecimalValue(financialExpenseData, currentPeriod);
            result.put("财务费用本年值", financialCurrent != null ? financialCurrent.intValue() : 0);
        }

        // 计算三项费用率
        // 需要从主要经济指标中获取主营业务收入数据来计算费用率
        Map<String, Object> params = new HashMap<>();
        params.put("queryYear", queryYear);
        params.put("compareYear", compareYear);
        params.put("startMonth", startMonth);
        params.put("endMonth", endMonth);

        try {
//            List<Map<String, Object>> mainIndicators = selectMainEconomicIndicators(params);
            String jsonData = "[{\"预算执行进度（%）\":\"52.12\",\"2025年预算数\":\"86,837.98\",\"2024年1-5月\":\"44,016.37\",\"项目\":\"税利总额\",\"2025年1-5月\":\"45,257.26\",\"同比增减（%）\":\"2.82\"},{\"预算执行进度（%）\":\"58.13\",\"2025年预算数\":\"31,006.79\",\"2024年1-5月\":\"17,128.29\",\"项目\":\"其中：利润总额\",\"2025年1-5月\":\"18,024.82\",\"同比增减（%）\":\"5.23\"},{\"预算执行进度（%）\":\"48.78\",\"2025年预算数\":\"55,831.19\",\"2024年1-5月\":\"26,888.08\",\"项目\":\"税费（不含所得税）\",\"2025年1-5月\":\"27,232.44\",\"同比增减（%）\":\"1.28\"},{\"预算执行进度（%）\":\"44.20\",\"2025年预算数\":\"89,250.00\",\"2024年1-5月\":\"39,224.00\",\"项目\":\"卷烟销售数量\",\"2025年1-5月\":\"39,448.00\",\"同比增减（%）\":\"0.57\"},{\"预算执行进度（%）\":\"46.75\",\"2025年预算数\":\"320,576.36\",\"2024年1-5月\":\"146,603.69\",\"项目\":\"卷烟销售收入\",\"2025年1-5月\":\"149,864.97\",\"同比增减（%）\":\"2.22\"},{\"预算执行进度（%）\":\"46.52\",\"2025年预算数\":\"223,653.31\",\"2024年1-5月\":\"101,856.67\",\"项目\":\"卷烟销售成本\",\"2025年1-5月\":\"104,054.32\",\"同比增减（%）\":\"2.16\"},{\"预算执行进度（%）\":\"47.26\",\"2025年预算数\":\"96,923.05\",\"2024年1-5月\":\"44,747.02\",\"项目\":\"毛利额\",\"2025年1-5月\":\"45,810.65\",\"同比增减（%）\":\"2.38\"},{\"预算执行进度（%）\":\"101.12\",\"2025年预算数\":\"30.23\",\"2024年1-5月\":\"30.52\",\"项目\":\"毛利率（%）\",\"2025年1-5月\":\"30.57\",\"同比增减（%）\":\"0.16\"},{\"预算执行进度（%）\":\"41.94\",\"2025年预算数\":\"70.00\",\"2024年1-5月\":\"42.72\",\"项目\":\"其他业务收入\",\"2025年1-5月\":\"29.36\",\"同比增减（%）\":\"-31.27\"},{\"预算执行进度（%）\":\"41.60\",\"2025年预算数\":\"196.00\",\"2024年1-5月\":\"75.95\",\"项目\":\"其他业务成本\",\"2025年1-5月\":\"81.54\",\"同比增减（%）\":\"7.36\"},{\"预算执行进度（%）\":\"33.13\",\"2025年预算数\":\"22,082.26\",\"2024年1-5月\":\"7,398.15\",\"项目\":\"三项费用总额\",\"2025年1-5月\":\"7,315.04\",\"同比增减（%）\":\"-1.12\"},{\"预算执行进度（%）\":\"70.83\",\"2025年预算数\":\"6.89\",\"2024年1-5月\":\"5.05\",\"项目\":\"三项费用率（%）\",\"2025年1-5月\":\"4.88\",\"同比增减（%）\":\"-3.37\"}]";
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, Object>> mainIndicators = mapper.readValue(jsonData, new TypeReference<List<Map<String, Object>>>() {});



            Map<String, Object> revenueData = findIndicatorByName(mainIndicators, "卷烟销售收入");

            if (revenueData != null && totalData != null) {
                BigDecimal revenue = getBigDecimalValue(revenueData, currentPeriod);
                BigDecimal lastYearRevenue = getBigDecimalValue(revenueData, comparePeriod);
                BigDecimal totalExpense = getBigDecimalValue(totalData, currentPeriod);
                BigDecimal lastYearTotalExpense = getBigDecimalValue(totalData, comparePeriod);

                if (revenue != null && totalExpense != null && revenue.compareTo(BigDecimal.ZERO) != 0) {
                    // 三项费用率（当年）
                    BigDecimal expenseRate = totalExpense.multiply(new BigDecimal("100"))
                            .divide(revenue, 2, RoundingMode.HALF_UP);
                    result.put("三项费用率", expenseRate);

                    // 三项费用率（上年）
                    if (lastYearRevenue != null && lastYearTotalExpense != null && lastYearRevenue.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal lastYearExpenseRate = lastYearTotalExpense.multiply(new BigDecimal("100"))
                                .divide(lastYearRevenue, 2, RoundingMode.HALF_UP);
                        result.put("上年三项费用率", lastYearExpenseRate);

                        // 三项费用率同比变化
                        BigDecimal rateChange = expenseRate.subtract(lastYearExpenseRate);
                        result.put("三项费用率同比变化", rateChange.setScale(2, RoundingMode.HALF_UP));

                        if (rateChange.compareTo(BigDecimal.ZERO) > 0) {
                            result.put("三项费用率变化方向", "增长");
                        } else if (rateChange.compareTo(BigDecimal.ZERO) < 0) {
                            result.put("三项费用率变化方向", "下降");
                        } else {
                            result.put("三项费用率变化方向", "持平");
                        }
                    }
                }
            }
        } catch (Exception e) {
//            log.warn("计算三项费用率时出错", e);
            result.put("三项费用率", BigDecimal.ZERO);
        }

        return result;
    }


    /**
     * 计算卷烟经营情况分析各项指标值
     */
    private Map<String, Object> calculateCigaretteOperationIndicatorValues(List<Map<String, Object>> indicatorsData,
                                                                           Integer queryYear, Integer compareYear,
                                                                           Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (indicatorsData == null || indicatorsData.isEmpty()) {
            return result;
        }

        String currentPeriod =  queryYear + "年" + startMonth + "-" + endMonth + "月";
        String comparePeriod=  compareYear + "年" + startMonth + "-" + endMonth + "月";
        String queryYearBudget =  queryYear + "年预算数";

        // 当前时期
        result.put("当前时期", currentPeriod);

        // 从指标数据中提取各项值
        Map<String, Object> salesRevenueData = findIndicatorByName(indicatorsData, "卷烟销售收入");
        Map<String, Object> salesQuantityData = findIndicatorByName(indicatorsData, "卷烟销售数量");
        Map<String, Object> grossProfitData = findIndicatorByName(indicatorsData, "毛利额");
        Map<String, Object> grossProfitRateData = findIndicatorByName(indicatorsData, "毛利率（%）");

        // 卷烟销售收入和卷烟销量分析
        if (salesRevenueData != null) {
            BigDecimal currentValue = getBigDecimalValue(salesRevenueData, currentPeriod);
            BigDecimal lastYearValue = getBigDecimalValue(salesRevenueData, comparePeriod);

            result.put("卷烟销售收入本年值", currentValue != null ? currentValue.intValue() : 0);

            // 卷烟销售收入同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("卷烟销售收入同比增加额", increaseAmount.intValue());
            } else {
                result.put("卷烟销售收入同比增加额", 0);
            }

            // 卷烟销售收入同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("卷烟销售收入同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("卷烟销售收入同比增幅", BigDecimal.ZERO);
            }
        }

        if (salesQuantityData != null) {
            BigDecimal currentValue = getBigDecimalValue(salesQuantityData, currentPeriod);
            BigDecimal lastYearValue = getBigDecimalValue(salesQuantityData, comparePeriod);

            result.put("卷烟销售数量本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

            // 卷烟销售数量同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("卷烟销售数量同比增加额", increaseAmount.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("卷烟销售数量同比增加额", BigDecimal.ZERO);
            }

            // 卷烟销售数量同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("卷烟销售数量同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("卷烟销售数量同比增幅", BigDecimal.ZERO);
            }
        }

        // 卷烟结构分析 - 单箱收入计算
        BigDecimal salesRevenueCurrent = getBigDecimalValue(salesRevenueData, currentPeriod);
        BigDecimal salesQuantityCurrent = getBigDecimalValue(salesQuantityData, currentPeriod);
        BigDecimal salesRevenueLastYear = getBigDecimalValue(salesRevenueData, comparePeriod);
        BigDecimal salesQuantityLastYear = getBigDecimalValue(salesQuantityData, comparePeriod);
        BigDecimal salesRevenueBudget = getBigDecimalValue(salesRevenueData, queryYearBudget);
        BigDecimal salesQuantityBudget = getBigDecimalValue(salesQuantityData, queryYearBudget);

        if (salesRevenueCurrent != null && salesQuantityCurrent != null && salesQuantityCurrent.compareTo(BigDecimal.ZERO) != 0) {
            // 单箱含税收入计算值 (假设增值税率13%)
            BigDecimal unitRevenueWithTax = salesRevenueCurrent
                    .multiply(new BigDecimal("1.13"))
                    .multiply(new BigDecimal("10000"))
                    .divide(salesQuantityCurrent, 0, RoundingMode.HALF_UP);
            result.put("单箱含税收入计算值", unitRevenueWithTax.intValue());

            // 计算上年单箱收入
            if (salesRevenueLastYear != null && salesQuantityLastYear != null && salesQuantityLastYear.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal lastYearUnitRevenueWithTax = salesRevenueLastYear
                        .multiply(new BigDecimal("1.13"))
                        .multiply(new BigDecimal("10000"))
                        .divide(salesQuantityLastYear, 0, RoundingMode.HALF_UP);

                // 单箱收入同比增加额
                BigDecimal unitRevenueIncrease = unitRevenueWithTax.subtract(lastYearUnitRevenueWithTax);
                result.put("单箱收入同比增加额", unitRevenueIncrease.intValue());

                // 单箱收入同比增幅
                if (lastYearUnitRevenueWithTax.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal unitRevenueGrowthRate = unitRevenueIncrease
                            .divide(lastYearUnitRevenueWithTax, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    result.put("单箱收入同比增幅", unitRevenueGrowthRate.setScale(2, RoundingMode.HALF_UP));
                } else {
                    result.put("单箱收入同比增幅", BigDecimal.ZERO);
                }
            }

            // 年度单箱收入预算值
            if (salesRevenueBudget != null && salesQuantityBudget != null && salesQuantityBudget.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal budgetUnitRevenueWithTax = salesRevenueBudget
                        .multiply(new BigDecimal("1.13"))
                        .multiply(new BigDecimal("10000"))
                        .divide(salesQuantityBudget, 0, RoundingMode.HALF_UP);
                result.put("年度单箱收入预算值", budgetUnitRevenueWithTax.intValue());

                // 单箱收入超预算额
                BigDecimal unitRevenueExceedBudget = unitRevenueWithTax.subtract(budgetUnitRevenueWithTax);
                result.put("单箱收入超预算额", unitRevenueExceedBudget.intValue());
            }
        }

        // 卷烟销售毛利和毛利率分析
        if (grossProfitData != null) {
            BigDecimal currentValue = getBigDecimalValue(grossProfitData, currentPeriod);
            BigDecimal lastYearValue = getBigDecimalValue(grossProfitData, comparePeriod);

            result.put("毛利额本年值", currentValue != null ? currentValue.intValue() : 0);

            // 毛利额同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("毛利额同比增加额", increaseAmount.intValue());
            } else {
                result.put("毛利额同比增加额", 0);
            }

            // 毛利额同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("毛利额同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("毛利额同比增幅", BigDecimal.ZERO);
            }
        }

        if (grossProfitRateData != null) {
            BigDecimal currentValue = getBigDecimalValue(grossProfitRateData, currentPeriod);
            BigDecimal lastYearValue = getBigDecimalValue(grossProfitRateData, comparePeriod);
            BigDecimal budgetValue = getBigDecimalValue(grossProfitRateData, queryYearBudget);

            result.put("毛利率本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

            // 毛利率同比变化
            if (currentValue != null && lastYearValue != null) {
                BigDecimal changeAmount = currentValue.subtract(lastYearValue);
                result.put("毛利率同比变化绝对值", changeAmount.abs().setScale(2, RoundingMode.HALF_UP));

                if (changeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    result.put("毛利率同比变化方向", "提高");
                } else if (changeAmount.compareTo(BigDecimal.ZERO) < 0) {
                    result.put("毛利率同比变化方向", "下降");
                } else {
                    result.put("毛利率同比变化方向", "持平");
                }
            } else {
                result.put("毛利率同比变化绝对值", BigDecimal.ZERO);
                result.put("毛利率同比变化方向", "持平");
            }

            // 与预算比较情况描述
            if (currentValue != null && budgetValue != null) {
                BigDecimal budgetDiff = currentValue.subtract(budgetValue);
                if (budgetDiff.compareTo(BigDecimal.ZERO) > 0) {
                    result.put("与预算比较情况描述", "超过毛利率预算数" + budgetValue.setScale(2, RoundingMode.HALF_UP) + "%");
                } else if (budgetDiff.compareTo(BigDecimal.ZERO) < 0) {
                    result.put("与预算比较情况描述", "低于预算" + budgetDiff.abs().setScale(2, RoundingMode.HALF_UP) + "个百分点");
                } else {
                    result.put("与预算比较情况描述", "与预算持平");
                }
            } else {
                result.put("与预算比较情况描述", "预算数据不可用");
            }
        }

        return result;
    }


    /**
     * 计算重点费用支出情况分析各项指标值
     */
    private Map<String, Object> calculateKeyExpensesIndicatorValues(List<Map<String, Object>> keyExpensesData,
                                                                    Integer queryYear, Integer compareYear,
                                                                    Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (keyExpensesData == null || keyExpensesData.isEmpty()) {
            return result;
        }

        String currentPeriod =  queryYear + "年" + startMonth + "-" + endMonth + "月";
        String comparePeriod=  compareYear + "年" + startMonth + "-" + endMonth + "月";
        String queryYearBudget =  queryYear + "年预算数";

        // 当前时期
        result.put("当前时期", currentPeriod);

        // 截至日期
        int lastDay = getLastDayOfMonth(queryYear, endMonth);
        String deadlineDate = queryYear + "年" + endMonth + "月" + lastDay + "日";
        result.put("截至日期", deadlineDate);

        // 从重点费用数据中提取各项指标
        for (Map<String, Object> expenseData : keyExpensesData) {
            String itemName = (String) expenseData.get("预算项目");
            if (itemName == null) continue;

            BigDecimal currentValue = getBigDecimalValue(expenseData, currentPeriod);
            BigDecimal lastYearValue = getBigDecimalValue(expenseData, comparePeriod);
            BigDecimal budgetValue = getBigDecimalValue(expenseData, queryYearBudget);
            BigDecimal executionRate = getBigDecimalValue(expenseData, "预算执行率%");
            BigDecimal diffPercent = getBigDecimalValue(expenseData, "增减比率%");

            // 根据项目名称设置对应的变量
            switch (itemName) {
                case "两项费用率（%）":
                    result.put("两项费用率本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("两项费用率上年值", lastYearValue != null ? lastYearValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    if (currentValue != null && lastYearValue != null) {
                        BigDecimal change = currentValue.subtract(lastYearValue);
                        result.put("两项费用率同比变化", change.setScale(2, RoundingMode.HALF_UP));
                        result.put("两项费用率变化方向", change.compareTo(BigDecimal.ZERO) >= 0 ? "增长" : "下降");
                    }
                    break;

                case "会议费":
                    result.put("会议费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("会议费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("会议费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("会议费同比增幅", diffPercent != null ? diffPercent.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "车辆运行费":
                    result.put("车辆运行费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("车辆运行费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("车辆运行费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "福利费":
                    result.put("福利费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("福利费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("福利费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "实发工资":
                    result.put("实发工资本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("实发工资预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("实发工资执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    if (currentValue != null && lastYearValue != null) {
                        BigDecimal change = currentValue.subtract(lastYearValue);
                        result.put("实发工资同比变化额", change.setScale(2, RoundingMode.HALF_UP));
                        result.put("实发工资同比变化方向", change.compareTo(BigDecimal.ZERO) >= 0 ? "增加" : "减少");
                    }
                    result.put("实发工资同比变化幅度", diffPercent != null ? diffPercent.abs().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "零售终端建设费":
                    result.put("零售终端建设费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("零售终端建设费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "研发费":
                    result.put("研发费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("研发费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("研发费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;
            }
        }

        // 计算一些特殊的指标
        // 福利费占实发工资比例
        BigDecimal welfareAmount = (BigDecimal) result.get("福利费本年值");
        BigDecimal salaryAmount = (BigDecimal) result.get("实发工资本年值");
        if (welfareAmount != null && salaryAmount != null && salaryAmount.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal welfareRatio = welfareAmount.divide(salaryAmount, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            result.put("福利费占实发工资比例", welfareRatio.setScale(2, RoundingMode.HALF_UP));
        }

        return result;
    }

    /**
     * 计算实现税利情况分析各项指标值
     */
    private Map<String, Object> calculateTaxProfitIndicatorValues(List<Map<String, Object>> indicatorsData,
                                                                  Integer queryYear, Integer compareYear,
                                                                  Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (indicatorsData == null || indicatorsData.isEmpty()) {
            return result;
        }

        String queryDate =  queryYear + "年" + startMonth + "-" + endMonth + "月";
        String compareDate =  compareYear + "年" + startMonth + "-" + endMonth + "月";
        String queryYearBudget =  queryYear + "年预算数";

        // 从指标数据中提取各项值
        Map<String, Object> taxProfitData = findIndicatorByName(indicatorsData, "税利总额");
        Map<String, Object> profitData = findIndicatorByName(indicatorsData, "其中：利润总额");
        Map<String, Object> taxData = findIndicatorByName(indicatorsData, "税费（不含所得税）");

        if (taxProfitData != null) {

            // 税利总额本年值
            BigDecimal currentValue = getBigDecimalValue(taxProfitData, queryDate);
            BigDecimal lastYearValue = getBigDecimalValue(taxProfitData, compareDate);
            BigDecimal budgetValue = getBigDecimalValue(taxProfitData, queryYearBudget);
            BigDecimal executionRate = getBigDecimalValue(taxProfitData, "预算执行进度（%）");

            result.put("税利总额本年值", currentValue != null ? currentValue.intValue() : 0);

            // 税利同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("税利同比增加额", increaseAmount.intValue());
            } else {
                result.put("税利同比增加额", 0);
            }

            // 税利同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("税利同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("税利同比增幅", BigDecimal.ZERO);
            }

            // 全年税利预算数
            result.put("全年税利预算数", budgetValue != null ? budgetValue.intValue() : 0);

            // 税利预算执行进度
            result.put("税利预算执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        }

        if (profitData != null) {
            // 利润总额本年值
            BigDecimal currentValue = getBigDecimalValue(profitData, queryDate);
            BigDecimal lastYearValue = getBigDecimalValue(profitData, compareDate);

            result.put("利润总额本年值", currentValue != null ? currentValue.intValue() : 0);

            // 利润总额同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("利润总额同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("利润总额同比增幅", BigDecimal.ZERO);
            }
        }

        if (taxData != null) {
            // 税费本年值
            BigDecimal currentValue = getBigDecimalValue(taxData, queryDate);
            BigDecimal lastYearValue = getBigDecimalValue(taxData, compareDate);

            result.put("税费本年值", currentValue != null ? currentValue.intValue() : 0);

            // 税费同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("税费同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("税费同比增幅", BigDecimal.ZERO);
            }
        }

        // 当前时期末
        result.put("当前时期末", endMonth);

        // 当前时期末最后一天
        int lastDay = getLastDayOfMonth(queryYear, endMonth);
        result.put("当前时期末最后一天", lastDay);

        // 时间进度要求 (月份/12*100，保留两位小数)
        BigDecimal timeProgress = new BigDecimal(endMonth)
                .divide(new BigDecimal("12"), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        result.put("时间进度要求", timeProgress.setScale(2, RoundingMode.HALF_UP));

        // 进度差额
        BigDecimal executionRate = (BigDecimal) result.get("税利预算执行进度");
        if (executionRate != null) {
            BigDecimal progressDiff = executionRate.subtract(timeProgress);
            result.put("进度差额", progressDiff.setScale(2, RoundingMode.HALF_UP));
        } else {
            result.put("进度差额", BigDecimal.ZERO);
        }

        return result;
    }


    /**
     * 根据指标名称查找指标数据
     */
    private Map<String, Object> findIndicatorByName(List<Map<String, Object>> indicatorsData, String indicatorName) {
        if (indicatorsData == null || indicatorName == null) {
            return null;
        }

        return indicatorsData.stream()
                .filter(data -> indicatorName.equals(data.get("项目")) || indicatorName.equals(data.get("indicatorName")))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据费用名称查找费用数据
     */
    private Map<String, Object> findExpenseByName(List<Map<String, Object>> expensesData, String expenseName) {
        if (expensesData == null || expenseName == null) {
            return null;
        }

        return expensesData.stream()
                .filter(data -> expenseName.equals(data.get("预算项目")))
                .findFirst()
                .orElse(null);
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        if (data == null || key == null) {
            return BigDecimal.ZERO; // 或 null，根据你的需求
        }

        Object value = data.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return new BigDecimal(value.toString());
        } else if (value instanceof String) {
            try {
                // 关键：移除所有逗号
                String str = ((String) value).replaceAll(",", "").trim();
                if (str.isEmpty()) {
                    return BigDecimal.ZERO;
                }
                return new BigDecimal(str);
            } catch (NumberFormatException e) {
                // 可以打印日志
                // System.err.println("无法解析数字: " + value);
                return BigDecimal.ZERO; // 或返回 null
            }
        }

        return BigDecimal.ZERO; // 或 null
    }

    /**
     * 获取指定年月的最后一天
     */
    private int getLastDayOfMonth(int year, int month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            case 2:
                // 判断闰年
                if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                    return 29;
                } else {
                    return 28;
                }
            default:
                return 31;
        }
    }

}
