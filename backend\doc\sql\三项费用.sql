WITH base_data AS (
    SELECT
        i.id,
        i.code,
        CASE
            WHEN i.name = '劳务费用（管理费用）' THEN '劳务费（管理费用）'
            ELSE i.name
        END AS indicator_name,
        EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) AS year,
        EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) AS month,
        iv.value
    FROM yjzb_indicator i
    JOIN yjzb_indicator_values iv ON i.id = iv.indicator_id
    WHERE EXTRACT(YEAR FROM TO_DATE(iv.period, 'YYYY-MM')) IN (2024, 2025)
      AND EXTRACT(MONTH FROM TO_DATE(iv.period, 'YYYY-MM')) BETWEEN 1 AND 5
),

-- 汇总销售费用和管理费用的各项明细
fee_details AS (
    -- 销售费用明细
    SELECT
        CASE
            WHEN indicator_name LIKE '%（销售费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
            ELSE indicator_name
        END AS item_name,
        'sales' AS fee_type,
        year,
        SUM(value) AS value
    FROM base_data
    WHERE indicator_name LIKE '%（销售费用）'
    GROUP BY
        CASE
            WHEN indicator_name LIKE '%（销售费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
            ELSE indicator_name
        END,
        year

    UNION ALL

    -- 管理费用明细
    SELECT
        CASE
            WHEN indicator_name LIKE '%（管理费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
            ELSE indicator_name
        END AS item_name,
        'management' AS fee_type,
        year,
        SUM(value) AS value
    FROM base_data
    WHERE indicator_name LIKE '%（管理费用）'
    GROUP BY
        CASE
            WHEN indicator_name LIKE '%（管理费用）' THEN SUBSTRING(indicator_name, 1, POSITION('（' IN indicator_name) - 1)
            ELSE indicator_name
        END,
        year
),

-- 合并销售费用和管理费用的同名项目
merged_fees AS (
    SELECT
        item_name,
        year,
        SUM(value) AS value
    FROM fee_details
    GROUP BY item_name, year
),

-- 获取财务费用数据
financial_fee AS (
    SELECT
        indicator_name AS item_name,
        year,
        SUM(value) AS value
    FROM base_data
    WHERE indicator_name in ('财务费用', '管理费用', '销售费用')
    GROUP BY year, indicator_name
),

-- 获取研发费用数据
rd_fee AS (
    SELECT
        '企业研发费用' AS item_name,
        year,
        SUM(value) AS value
    FROM base_data
    WHERE indicator_name LIKE '%企业研发费用%' and code = 'qiyeyanfa_guanli'
    GROUP BY year
),

-- 合并所有费用项目
all_fees AS (
    SELECT * FROM merged_fees
    UNION ALL
    SELECT * FROM financial_fee
    UNION ALL
    SELECT * FROM rd_fee
),

-- 透视数据
pivot_data AS (
    SELECT
        item_name,
        MAX(CASE WHEN year = 2025 THEN value END) AS v_2025_actual,
        MAX(CASE WHEN year = 2024 THEN value END) AS v_2024_actual
    FROM all_fees
    GROUP BY item_name
),

-- 获取预算数据 - 从年度预算表获取
budget_data AS (
    SELECT
        CASE
            WHEN i.name LIKE '%（销售费用）' THEN SUBSTRING(i.name, 1, POSITION('（' IN i.name) - 1)
            WHEN i.name LIKE '%（管理费用）' THEN SUBSTRING(i.name, 1, POSITION('（' IN i.name) - 1)
            WHEN i.name = '劳务费用（管理费用）' THEN '劳务费（管理费用）'
            ELSE i.name
        END AS item_name,
        -- 优先使用中期调整预算数，否则使用年初预算数
        SUM(COALESCE(iab.current_used, 0)) AS budget_value
    FROM yjzb_indicator i
    LEFT JOIN yjzb_indicator_annual_budget iab ON i.id = iab.indicator_id
        AND iab.year = 2025
        AND iab.is_deleted = 0
    WHERE i.is_deleted = 0
      AND (i.name LIKE '%（销售费用）' OR i.name LIKE '%（管理费用）' OR i.name = '财务费用' OR i.name = '销售费用' OR i.name = '管理费用' OR i.name LIKE '%企业研发费用%')
    GROUP BY
        CASE
            WHEN i.name LIKE '%（销售费用）' THEN SUBSTRING(i.name, 1, POSITION('（' IN i.name) - 1)
            WHEN i.name LIKE '%（管理费用）' THEN SUBSTRING(i.name, 1, POSITION('（' IN i.name) - 1)
            WHEN i.name = '劳务费用（管理费用）' THEN '劳务费（管理费用）'
            ELSE i.name
        END
),

-- 合并实际值和预算值
final_data AS (
    SELECT
        (CASE
            WHEN p.item_name = '打假经费' THEN '专卖打假经费'
            WHEN p.item_name = '打私经费' THEN '专卖打私经费'
            ELSE p.item_name
        END) as item_name,
        p.v_2025_actual,
        p.v_2024_actual,
        COALESCE(p.v_2025_actual, 0) - COALESCE(p.v_2024_actual, 0) AS diff_amount,
        CASE
            WHEN COALESCE(p.v_2024_actual, 0) = 0 THEN NULL
            ELSE (COALESCE(p.v_2025_actual, 0) - COALESCE(p.v_2024_actual, 0)) / COALESCE(p.v_2024_actual, 0) * 100
        END AS diff_percent,
        COALESCE(b.budget_value, 0) AS budget_value,
        COALESCE(b.budget_value, 0) - COALESCE(p.v_2025_actual, 0) AS budget_balance,
        CASE
            WHEN COALESCE(b.budget_value, 0) = 0 THEN NULL
            ELSE COALESCE(p.v_2025_actual, 0) / COALESCE(b.budget_value, 0) * 100
        END AS budget_execution_rate
    FROM pivot_data p
    LEFT JOIN budget_data b ON p.item_name = b.item_name
    where p.item_name in (
        '修理费', '财务费用', '管理费用', '销售费用', '打假经费', '打私经费', '企业文化建设费', '中介费', '无形资产摊销', '劳务费', '绿化费', '业务招待费', '会议费', '协会会费', '零售终端建设费', '文明吸烟环境建设费', '交易手续费', '长期待摊费用摊销', '政府性基金', '劳动保护费', '书报费', '通讯费', '车杂费', '租赁费', '物业管理费', '保险费', '信息系统维护费', '水电费', '其他', '网络通讯费', '燃料费', '低值易耗品摊销', '包装费', '专卖管理经费', '差旅费', '企业研发费用', '党组织工作经费', '警卫消防费', '折旧费', '办公费', '职工薪酬'
    )
),

-- 计算合计行
total_row AS (
    SELECT
        '合计' AS item_name,
        SUM(v_2025_actual) AS v_2025_actual,
        SUM(v_2024_actual) AS v_2024_actual,
        SUM(diff_amount) AS diff_amount,
        CASE
            WHEN SUM(v_2024_actual) = 0 THEN NULL
            ELSE SUM(diff_amount) / SUM(v_2024_actual) * 100
        END AS diff_percent,
        SUM(budget_value) AS budget_value,
        SUM(budget_balance) AS budget_balance,
        CASE
            WHEN SUM(budget_value) = 0 THEN NULL
            ELSE SUM(v_2025_actual) / SUM(budget_value) * 100
        END AS budget_execution_rate
    FROM final_data
),

-- 合并所有数据
complete_data AS (
    SELECT * FROM total_row
    UNION ALL
    SELECT * FROM final_data
)

-- 最终输出
SELECT
    item_name AS "预算项目",
    CASE
        WHEN item_name = '合计' THEN 2
        ELSE ROW_NUMBER() OVER (ORDER BY diff_amount DESC) + 2
    END AS "行次",
    TO_CHAR(ROUND(v_2025_actual / 10000, 2), 'FM999,999,990.00') AS "2025年1-5月",
    TO_CHAR(ROUND(v_2024_actual / 10000, 2), 'FM999,999,990.00') AS "2024年1-5月",
    TO_CHAR(ROUND(diff_amount / 10000, 2), 'FM999,999,990.00') AS "增减额",
    CASE
        WHEN diff_percent IS NULL THEN ''
        ELSE TO_CHAR(ROUND(diff_percent, 2), 'FM9990.00')
    END AS "增减比率%",
    TO_CHAR(ROUND(budget_value / 10000, 2), 'FM999,999,990.00') AS "2025年预算数",
    TO_CHAR(ROUND(budget_balance / 10000, 2), 'FM999,999,990.00') AS "预算余额",
    CASE
        WHEN budget_execution_rate IS NULL THEN ''
        ELSE TO_CHAR(ROUND(budget_execution_rate, 2), 'FM9990.00')
    END AS "预算执行率%"
FROM complete_data
ORDER BY
    CASE WHEN item_name = '合计' THEN 0 ELSE 1 END,
    diff_amount DESC,
    diff_percent DESC;