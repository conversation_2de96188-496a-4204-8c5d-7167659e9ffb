-- 财务分析报告列表表
CREATE TABLE IF NOT EXISTS yjzb_finance_analysis_report (
  id              BIGINT PRIMARY KEY,
  title           VARCHAR(255) NOT NULL,
  type            VARCHAR(50),
  period          VARCHAR(100),
  query_year      INTEGER,
  compare_year    INTEGER,
  start_month     INTEGER,
  end_month       INTEGER,
  report_status   VARCHAR(20) DEFAULT 'generating',
  file_path       VARCHAR(500),
  file_name       VARCHAR(255),
  file_size       BIGINT,
  pdf_file_path VARCHAR(500),
  pdf_file_name VARCHAR(255),
  pdf_file_size BIGINT,
  download_count  INTEGER DEFAULT 0,
  generate_time   TIMESTAMP(6) DEFAULT NOW(),
  complete_time   TIMESTAMP(6),
  error_message   TEXT,
  tenant_id       VARCHAR(12),
  create_user     BIGINT,
  create_dept     BIGINT,
  create_time     TIMESTAMP(6) WITH TIME ZONE DEFAULT NOW(),
  update_user     BIGINT,
  update_time     TIMESTAMP(6) WITH TIME ZONE DEFAULT NOW(),
  status          INTEGER DEFAULT 1,
  is_deleted      INTEGER DEFAULT 0
);




-- 添加表注释
COMMENT ON TABLE yjzb_finance_analysis_report IS '财务分析报告列表表';

-- 添加列注释
COMMENT ON COLUMN yjzb_finance_analysis_report.title IS '报告标题';
COMMENT ON COLUMN yjzb_finance_analysis_report.type IS '报告类型';
COMMENT ON COLUMN yjzb_finance_analysis_report.period IS '分析周期';
COMMENT ON COLUMN yjzb_finance_analysis_report.query_year IS '查询年份';
COMMENT ON COLUMN yjzb_finance_analysis_report.compare_year IS '对比年份';
COMMENT ON COLUMN yjzb_finance_analysis_report.start_month IS '开始月份';
COMMENT ON COLUMN yjzb_finance_analysis_report.end_month IS '结束月份';
COMMENT ON COLUMN yjzb_finance_analysis_report.report_status IS '状态：generating-生成中，completed-已完成，failed-失败';
COMMENT ON COLUMN yjzb_finance_analysis_report.file_path IS '文件路径';
COMMENT ON COLUMN yjzb_finance_analysis_report.file_name IS '文件名称';
COMMENT ON COLUMN yjzb_finance_analysis_report.file_size IS '文件大小(字节)';
COMMENT ON COLUMN yjzb_finance_analysis_report.download_count IS '下载次数';
COMMENT ON COLUMN yjzb_finance_analysis_report.generate_time IS '生成时间';
COMMENT ON COLUMN yjzb_finance_analysis_report.complete_time IS '完成时间';
COMMENT ON COLUMN yjzb_finance_analysis_report.error_message IS '错误信息';
COMMENT ON COLUMN yjzb_finance_analysis_report.tenant_id IS '租户ID';
COMMENT ON COLUMN yjzb_finance_analysis_report.create_user IS '创建人';
COMMENT ON COLUMN yjzb_finance_analysis_report.create_dept IS '创建部门';
COMMENT ON COLUMN yjzb_finance_analysis_report.create_time IS '创建时间';
COMMENT ON COLUMN yjzb_finance_analysis_report.update_user IS '更新人';
COMMENT ON COLUMN yjzb_finance_analysis_report.update_time IS '更新时间';
COMMENT ON COLUMN yjzb_finance_analysis_report.status IS '状态字段（逻辑使用）';
COMMENT ON COLUMN yjzb_finance_analysis_report.is_deleted IS '是否删除（0:否,1:是）';

-- 添加字段注释
COMMENT ON COLUMN yjzb_finance_analysis_report.pdf_file_path IS 'PDF文件路径';
COMMENT ON COLUMN yjzb_finance_analysis_report.pdf_file_name IS 'PDF文件名称';
COMMENT ON COLUMN yjzb_finance_analysis_report.pdf_file_size IS 'PDF文件大小(字节)';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_finance_analysis_report_report_status ON yjzb_finance_analysis_report(report_status);
CREATE INDEX IF NOT EXISTS idx_finance_analysis_report_type ON yjzb_finance_analysis_report(type);
CREATE INDEX IF NOT EXISTS idx_finance_analysis_report_generate_time ON yjzb_finance_analysis_report(generate_time);

-- 注意：原语句中 idx_finance_analysis_report_report_id 是错误的，因为没有 report_id 字段
-- 如果你有外键或唯一编号需求，应确认字段是否存在